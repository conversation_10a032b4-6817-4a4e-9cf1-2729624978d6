security:
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

    providers:
        app_user_provider:
            entity:
                class: App\Entity\User
                property: username  # Utilisez 'email' si vous vous basez sur l'email pour l'authentification

    firewalls:
        # the order in which firewalls are defined is very important, as the
        # request will be handled by the first firewall whose pattern matches
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        # a firewall with no pattern should be defined last because it will match all requests
        main:
            json_login:
                check_path: api_login
            lazy: true
            # provider that you set earlier inside providers
            provider: app_user_provider
            form_login:
                login_path: app_login
                check_path: app_login
                enable_csrf: true
                default_target_path: app_chantier
            logout:
                path: app_logout
                target: app_login
    access_control:
        - { path: ^/chantier, roles: IS_AUTHENTICATED_FULLY }