# Système de Recalcul Automatique des Chantiers

## Vue d'ensemble

Le système de recalcul automatique des chantiers a été mis en place pour résoudre le problème où l'ajout de semaines interdites n'entraînait pas automatiquement le recalcul des chantiers en cours.

## Fonctionnalités

### 1. Recalcul Automatique

Lorsque vous ajoutez ou supprimez une semaine interdite, le système :
- Recharge automatiquement la liste des semaines interdites
- Recalcule tous les chantiers en cours (non archivés)
- Met à jour les étapes qui tombent sur des semaines interdites
- Affiche un résumé des modifications effectuées

### 2. Recalcul Manuel

Un bouton "Recalculer les chantiers" est disponible pour déclencher manuellement le recalcul :
- Accessible via l'interface web
- Disponible en ligne de commande
- Utile après des modifications en masse

### 3. Ligne de Commande

```bash
# Recalcul standard
php bin/console app:recalculer-chantiers

# Simulation (sans sauvegarde)
php bin/console app:recalculer-chantiers --dry-run

# Avec détails des modifications
php bin/console app:recalculer-chantiers --verbose
```

## Architecture Technique

### Services Créés

1. **ChantierRecalculService** (`src/Service/ChantierRecalculService.php`)
   - Service principal pour le recalcul des chantiers
   - Gère le recalcul de tous les chantiers ou d'un chantier spécifique
   - Optimisé pour ne recalculer que les chantiers affectés

2. **SemaineCalculatorService** (modifié)
   - Ajout de la méthode `recalculerChantier()`
   - Amélioration de la gestion des semaines interdites

### Contrôleurs Modifiés

- **SemaineInterditeController** : Intégration du recalcul automatique dans les actions d'ajout/suppression

### Nouvelles Routes

- `POST /semaines-interdites/recalculer-chantiers` : Recalcul manuel via API

## Utilisation

### Interface Web

1. **Ajout d'une semaine interdite** :
   - Le recalcul se déclenche automatiquement
   - Une notification affiche le nombre de chantiers modifiés
   - Un modal détaillé s'ouvre si des modifications importantes sont détectées

2. **Suppression d'une semaine interdite** :
   - Même processus que l'ajout
   - Les chantiers peuvent être "libérés" de contraintes

3. **Recalcul manuel** :
   - Bouton disponible dans l'interface des semaines interdites
   - Utile après des modifications en base de données directes

### Ligne de Commande

```bash
# Vérifier l'impact avant de sauvegarder
php bin/console app:recalculer-chantiers --dry-run --verbose

# Appliquer les modifications
php bin/console app:recalculer-chantiers
```

## Logique de Recalcul

### Étapes du Processus

1. **Rechargement des semaines interdites** : Mise à jour du cache des semaines interdites
2. **Récupération des chantiers en cours** : Sélection des chantiers non archivés avec une date de début
3. **Recalcul par chantier** :
   - Pour chaque étape du chantier
   - Calcul de la nouvelle semaine en évitant les semaines interdites
   - Comparaison avec la semaine actuelle
   - Mise à jour si nécessaire
4. **Sauvegarde** : Persistance de toutes les modifications en une seule transaction

### Optimisations

- **Recalcul sélectif** : Possibilité de ne recalculer que les chantiers affectés par des semaines spécifiques
- **Traitement par lot** : Toutes les modifications sont sauvegardées en une seule fois
- **Logging** : Enregistrement des opérations pour le débogage

## Monitoring et Logs

### Logs Générés

- Nombre de chantiers recalculés
- Nombre de chantiers modifiés
- Détails des modifications par chantier
- Erreurs éventuelles

### Métriques Disponibles

```php
$resultats = [
    'chantiersRecalcules' => 15,      // Nombre total de chantiers vérifiés
    'chantiersModifies' => 3,         // Nombre de chantiers avec modifications
    'etapesTotalesModifiees' => 8,    // Nombre total d'étapes recalculées
    'details' => [...]               // Détails par chantier modifié
];
```

## Tests

### Tests Unitaires

- `tests/Service/ChantierRecalculServiceTest.php`
- Couvre les scénarios principaux de recalcul
- Mocks des dépendances pour l'isolation

### Tests d'Intégration

```bash
# Lancer les tests
php bin/phpunit tests/Service/ChantierRecalculServiceTest.php
```

## Interface Utilisateur

### Notifications

- **Succès** : Affichage du nombre de chantiers modifiés
- **Information** : Aucune modification nécessaire
- **Erreur** : Problèmes lors du recalcul

### Modal Détaillé

- Liste des chantiers modifiés
- Détail des étapes recalculées
- Anciennes vs nouvelles semaines
- Semaines interdites évitées

## Maintenance

### Commandes Utiles

```bash
# Vérification de l'état des chantiers
php bin/console app:recalculer-chantiers --dry-run

# Recalcul après maintenance
php bin/console app:recalculer-chantiers

# Logs détaillés
php bin/console app:recalculer-chantiers --verbose
```

### Dépannage

1. **Chantiers non recalculés** :
   - Vérifier que le chantier n'est pas archivé
   - Vérifier la présence d'une date de début

2. **Performances lentes** :
   - Vérifier le nombre de chantiers en cours
   - Considérer le recalcul sélectif

3. **Erreurs de calcul** :
   - Vérifier la cohérence des semaines interdites
   - Contrôler les décalages d'étapes

## Évolutions Futures

### Améliorations Possibles

1. **Recalcul asynchrone** : Pour les gros volumes de chantiers
2. **Historique des recalculs** : Traçabilité des modifications
3. **Notifications par email** : Alertes pour les modifications importantes
4. **API REST complète** : Exposition de toutes les fonctionnalités
5. **Interface de comparaison** : Avant/après les modifications

### Intégrations

- **Système de notifications** : Intégration avec un système de notifications push
- **Audit trail** : Enregistrement des modifications pour la conformité
- **Reporting** : Génération de rapports sur l'impact des semaines interdites
