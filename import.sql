INSERT INTO artisan (nom, type) VALUES
    ("CIS<PERSON>", "<PERSON>ssement"),
    ("LEFAUX", "Terrassement"),
    ("CLIENT", "Terrassement"),
    ("DANGEUL", "Terrassement"),
    ("CHARTIER", "Terrassement"),
    ("CV TERRASS", "Terrassement"),
    ("LPS TERRASSEMENT", "Terrassement"),
    ("PINEAU", "Terrassement"),
    ("ATTP", "Terrassement"),
    ("GALASSO", "Terrassement"),
    ("CHAPLAIN", "Terrassement"),
    
    ("BERTHE", "Maconnerie"),
    ("TRADINOVA", "Maconnerie"),
    ("Bourgine BTP", "<PERSON><PERSON>ie"),
    ("CAMEMBERT", "Macon<PERSON>ie"),
    ("SEZEN", "<PERSON><PERSON>ie"),
    ("LEGUILLON", "Maconnerie"),
    ("DANGEUL", "<PERSON><PERSON>ie"),
    ("<PERSON>AR<PERSON>ER", "<PERSON><PERSON>ie"),
    ("LEFAUX", "<PERSON><PERSON>ie"),
    ("SG CONSTRUCTIONS", "<PERSON><PERSON>ie"),
    ("GOME<PERSON>", "Maconnerie"),
    ("<PERSON>IN<PERSON>", "<PERSON><PERSON>ie"),
    ("HEURTEBISE", "Maconnerie"),
    
    ("EGB", "PlomberieElec"),
    ("MAILLARD 3P", "PlomberieElec"),
    ("HERBRON", "PlomberieElec"),
    ("LEJEUNE", "PlomberieElec"),
    ("CLIENTS", "PlomberieElec"),
    
    ("CB2C", "Charpente"),
    ("TOITURE OIZ.", "Charpente"),
    ("BM TOITURES", "Charpente"),
    ("TOIT DU BEL.", "Charpente"),
    ("BERTHE", "Charpente"),
    ("3C", "Charpente"),
    ("GRENECHE", "Charpente"),
    ("CHENEAU", "Charpente"),
    ("PINTO", "Charpente"),
    ("RAMAUGE", "Charpente"),
    ("VEJ", "Charpente"),
    ("CHAUVIERE", "Charpente"),
    ("HARDOIN", "Charpente"),
    
    ("CLIMELEC", "Etancheite"),
    ("SARL FAHARDINE", "Etancheite"),
    
    ("ALU'G", "Zinguerie"),
    ("ATOUT ALU", "Zinguerie"),
    ("CB2C", "Zinguerie"),
    
    ("GOUESSE N.", "Menuiserie"),
    ("JD CREA", "Menuiserie"),
    ("ROUSSEAU D.", "Menuiserie"),
    ("BBC", "Menuiserie"),
    ("CARLOS", "Menuiserie"),
    ("EDO", "Menuiserie"),
    ("INSTINCT MENUISERIES", "Menuiserie"),
    
    ("JEUDON M.", "Placo"),
    ("JORDI DUVAL", "Placo"),
    ("PENCHE S.", "Placo"),
    ("FLEURY", "Placo"),
    ("COIGNARD", "Placo"),
    ("MONTAGNE J.", "Placo"),
    ("CARLOS", "Placo"),
    ("COQUEMONT", "Placo"),
    ("NOSLIERE", "Placo"),
    ("BESNARD", "Placo"),
    ("CLIENT", "Placo"),
    ("LUSSON", "Placo"),
    ("GOSNET", "Placo"),
    ("LUDO ROBERT", "Placo"),
    
    ("Cd sols", "Chappe"),
    
    ("STR", "Enduit"),
    ("MDC RAVALEMENT", "Enduit"),
    ("MARTIN", "Enduit"),
    ("FERRERA M.", "Enduit"),
    ("Sergio", "Enduit"),
    ("MOREIRA", "Enduit"),
    ("CHARTIER", "Enduit"),
    ("Enduit et compagnie", "Enduit"),
    ("FFR", "Enduit"),
    ("VENDÔME", "Enduit"),
    
    ("ISODEAL", "LaineSouffle"),
    
    ("CONCEPT CARR.", "Carrelage"),
    ("CKARRELAGE", "Carrelage"),
    ("GREORY M.", "Carrelage"),
    ("GOUPIL J.", "Carrelage"),
    ("FOUQUERAY A.", "Carrelage"),
    ("CLIENT", "Carrelage"),
    ("BATICERAME", "Carrelage"),
    ("GRISON", "Carrelage"),
    ("LACHAUD", "Carrelage"),
    ("SAUSSEREAU", "Carrelage"),
    ("HLG", "Carrelage"),
    
    ("GOHIER", "Escalier"),
    ("CLIENT", "Escalier"),
    ("NON", "Escalier"),
    ("LEHOUX", "Escalier"),
    
    ("Laurent", "Finition"),
    
    ("FOUCHER", "Peinture"),
    ("CLIENT", "Peinture"),
    ("Direct FOUCH.", "Peinture"),
    
    ("ARTISANPERMEABILITE", "Permeabilite");


INSERT INTO chantier (client, commune, cdt, date_deb) VALUES
    ("ANDRADE", "LA BAZOGE", "SÉBASTIEN", "2024-01-15"),
    ("AUBIER", "NEUVILLE SUR SARTHE", "ROMUALD", "2024-02-12"),
    ("AVRIL - DEZAIRE", "YVRÉ L'EVEQUE", "HUGO", "2023-12-19"),
    ("BERTOTTI", "COULAINES", "SÉBASTIEN", NULL),
    ("BEURIER", "CHANGÉ", "HUGO", "2024-11-13"),
    ("BOUCLE DUSSUTOUR", "FILLE", "ROMUALD", "2024-01-09"),
    ("BOURGINE - CHAMPION", "MONCE EN BELIN", "ROMUALD", NULL),
    ("BOURGOING DROUIN", "MONTFORT LE GESNOIS", "HUGO", "2024-01-18"),
    ("BRUNELOT - GESLIN", "NEUVILLE SUR SARTHE", "ROMUALD", "2024-02-19"),
    ("BURGAUD - HERON", "NEUVILLE SUR SARTHE", "ROMUALD", "2024-07-26"),
    ("CABINET PARA", "TELOCHE", "SÉBASTIEN", NULL),
    ("CANIVET", "YVRE", "HUGO", "2023-10-13"),
    ("CHALIGNE - JACQUIN", "SARGE LES LE MANS", "HUGO", NULL),
    ("CHARPENTIER - COLLET", "LOUPLANDE", "ROMUALD", "2024-01-22"),
    ("CRAPIS COTTE EXT", "CHANGE", "SÉBASTIEN", NULL),
    ("CRAPIS COTTE RENO", "CHANGE", "SÉBASTIEN", NULL),
    ("DELAIN - BRIER", "MULSANNE", "HUGO", "2024-08-01"),
    ("DUTOUR - DONNE", "MONCE EN BELIN", "ROMUALD", "2024-04-29"),
    ("EDON", "SARGE LES LE MANS", "HUGO", "2024-10-15"),
    ("ESNAULT", "SARGE LES LE MANS", "HUGO", NULL),
    ("FERRET", "CHANGE", "HUGO", "2023-11-14"),
    ("GARNIER - LELOUP", "SAINT GERVAIS EN BELIN", "HUGO", "2024-06-13"),
    ("GUILLARDEAU", "MONCE EN BELIN", "ROMUALD", "2024-06-17"),
    ("HATTON", "BEAUFAY", "HUGO", "2024-07-02"),
    ("JEANNE - DUCEAU", "LE MANS", "SÉBASTIEN", "2023-05-31"),
    ("JOUIE", "SAINT GEORGES DU BOIS", "ROMUALD", "2023-12-13"),
    ("JOURDAINE", "MARIGNE LAILLE", "HUGO", "2024-03-14"),
    ("LEMARCHAND", "ARNAGE", "ROMUALD", "2024-07-25"),
    ("LUONG", "LE MANS", "HUGO", "2024-07-17"),
    ("MONIER", "SARGE LES LE MANS", "HUGO", "2024-10-17"),
    ("MOUSSADEK", "CHANGE", "HUGO", "2023-11-23"),
    ("PELTIER", "MONCE EN BELIN", "ROMUALD", "2024-04-02"),
    ("RIBOT - AMO BESNARD", "SARGE LES LE MANS", "HUGO", "2024-10-16"),
    ("ROUILLER", "PARIGNE L'EVEQUE", "ROMUALD", "2024-03-04"),
    ("ROUSSET", "FILLE SUR SARTHE", "HUGO", "2024-08-30"),
    ("RUFLET", "YVRE L'EVEQUE", "HUGO", "2024-09-18"),
    ("SAIVET", "LE MANS", "HUGO", "2024-06-11"),
    ("SARL ACMANS", "LE MANS", "ROMUALD", "2024-07-17"),
    ("VILAIN", "PARIGNE L'EVEQUE", "ROMUALD", "2024-06-28");


INSERT INTO bbc_ddb.terrassement (terrassier_id, branchement, etat_branchement, observation) VALUES
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- ANDRADE
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- AUBIER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- AVRIL - DEZAIRE
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- BERTOTTI
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- BEURIER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- BOUCLE DUSSUTOUR
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- BOURGINE - CHAMPION
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- BOURGOING DROUIN
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- BRUNELOT - GESLIN
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), 45, 'PAS FAIT', ''), -- BURGAUD - HERON
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- CABINET PARA
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- CANIVET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- CHALIGNE - JACQUIN
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- CHARPENTIER - COLLET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), 50, 'PAS FAIT', '6'), -- CRAPIS COTTE EXT
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), 1, 'PAS FAIT', ''), -- CRAPIS COTTE RENO
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', '41'), -- DELAIN - BRIER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- DUTOUR - DONNE
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- EDON
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- ESNAULT
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- FERRET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- GARNIER - LELOUP
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', '36'), -- GUILLARDEAU
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- HATTON
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- JEANNE - DUCEAU
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- JOUIE
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- JOURDAINE
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- LEMARCHAND
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', '41'), -- LUONG
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- MONIER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- MOUSSADEK
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- PELTIER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- RIBOT - AMO BESNARD
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- ROUILLER
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''), -- ROUSSET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- RUFLET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'FAIT', ''), -- SAIVET
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'PAS FAIT', ''), -- SARL ACMANS
    ((SELECT id FROM bbc_ddb.artisan WHERE nom = 'CV TERRASS'), NULL, 'NON', ''); -- VILAIN
    
UPDATE bbc_ddb.chantier SET terrassement_id = id;

INSERT INTO `bbc_ddb`.`maconnerie` (
    `maconnerie`.`macon_id`,
    `maconnerie`.`fondation`,
    `maconnerie`.`etat_fondation`,
    `maconnerie`.`dalle`,
    `maconnerie`.`etat_dalle`,
    `maconnerie`.`elevation`,
    `maconnerie`.`etat_elevation`,
    `maconnerie`.`rampannage`,
    `maconnerie`.`etat_rampannage`,
    `maconnerie`.`releve_seuil`,
    `maconnerie`.`etat_releve_seuil`,
    `maconnerie`.`seuil`,
    `maconnerie`.`etat_seuil`,
    `maconnerie`.`terrasse`,
    `maconnerie`.`etat_terrasse`
)
VALUES
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 18, 'PAS FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'CHARTIER'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 10, 'PAS FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'DANGEUL'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', 44, 'PAS FAIT', 51, 'PAS FAIT', 51, 'PAS FAIT', 51, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'NON', 8, 'PAS FAIT', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', 48, 'PAS FAIT', 48, 'PAS FAIT', 48, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), 42, 'PAS FAIT', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'HEURTEBISE'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', 41, 'PAS FAIT', NULL, 'FAIT', 41, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', 46, 'PAS FAIT', NULL, 'NON', 46, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'HEURTEBISE'), NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'NON', NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), 42, 'PAS FAIT', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'HEURTEBISE'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), 42, 'PAS FAIT', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'PAS FAIT'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', 42, 'PAS FAIT', 48, 'PAS FAIT', 48, 'PAS FAIT', 48, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', 42, 'PAS FAIT', 45, 'PAS FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON',NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'NON', 46, 'PAS FAIT', NULL, 'NON',  NULL, 'NON'),
    ((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE type="Maçonnerie" and `nom` = 'Bourgine BTP'), NULL, 'FAIT', NULL, 'FAIT', 46, 'PAS FAIT', NULL, 'NON', 46, 'PAS FAIT', NULL, 'NON',NULL, 'NON');

UPDATE bbc_ddb.chantier SET maconnerie_id = id;

INSERT INTO `bbc_ddb`.`plomberie_elec`
(`plombier_electricien_id`,
`prepa_dalle`,
`etat_prepa_dalle`,
`prepa_filerie_plomberie`,
`etat_prepa_filerie_plomberie`,
`gros_oeuvre`,
`etat_gros_oeuvre`,
`pose_pacchaudiere`,
`etat_pose_pacchaudiere`,
`finition_elec`,
`etat_finition_elec`,
`finition_plomberie_sanitaire`,
`etat_finition_plomberie_sanitaire`
)
VALUES
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 43, 'PAS FAIT', 51, 'PAS FAIT', 3, 'PAS FAIT', 50, 'PAS FAIT', 8, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', 26, 'PAS FAIT', 28, 'PAS FAIT', NULL, 'PAS FAIT', 36, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'LEJEUNE'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 44, 'PAS FAIT', NULL, 'FAIT', 43, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 46, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'FAIT', 50, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), 44, 'PAS FAIT', 7, 'PAS FAIT', 12, 'PAS FAIT', 14, 'PAS FAIT', 11, 'PAS FAIT', 20, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 50, 'PAS FAIT', 7, 'PAS FAIT', 10, 'PAS FAIT', 6, 'PAS FAIT', 11, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 24, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'NON', NULL, 'FAIT', NULL, 'FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT', 48, 'PAS FAIT', 44, 'PAS FAIT', 51, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'FAIT', 45, 'PAS FAIT', 48, 'PAS FAIT', 44, 'PAS FAIT', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 51, 'PAS FAIT', 4, 'PAS FAIT', 6, 'PAS FAIT', 6, 'PAS FAIT', 10, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'LEJEUNE'), NULL, 'FAIT', NULL, 'FAIT', 47, 'PAS FAIT', 49, 'PAS FAIT', 46, 'PAS FAIT', 6, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 42, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 41, 'PAS FAIT', 44, 'PAS FAIT', 46, 'PAS FAIT', 45, 'PAS FAIT', 50, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', 45, 'PAS FAIT', 48, 'PAS FAIT', 47, 'PAS FAIT', 50, 'PAS FAIT', 2, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 45, 'PAS FAIT', 48, 'PAS FAIT', 50, 'PAS FAIT', 50, 'PAS FAIT', 4, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 38, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 46, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 2, 'PAS FAIT', 7, 'PAS FAIT', 9, 'PAS FAIT', 6, 'PAS FAIT', 15, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), 40, 'PAS FAIT', 50, 'PAS FAIT', 3, 'PAS FAIT', 5, 'PAS FAIT', 5, 'PAS FAIT', 9, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 42, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT', NULL, 'FAIT', 49, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', NULL, 'FAIT', 45, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), 41, 'PAS FAIT', 2, 'PAS FAIT', 5, 'PAS FAIT', 7, 'PAS FAIT', 7, 'PAS FAIT', 11, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), 43, 'PAS FAIT', 50, 'PAS FAIT', 4, 'PAS FAIT', 6, 'PAS FAIT', 6, 'PAS FAIT', 11, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 44, 'PAS FAIT', 47, 'PAS FAIT', 49, 'PAS FAIT', 49, 'PAS FAIT', 2, 'PAS FAIT'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 4, 'PAS FAIT', 10, 'PAS FAIT', 12, 'PAS FAIT', 9, 'PAS FAIT', NULL, 'NON'),
((SELECT `id` FROM `bbc_ddb`.`artisan` WHERE `nom` = 'EGB'), NULL, 'FAIT', 3, 'PAS FAIT', 6, 'PAS FAIT', 8, 'PAS FAIT', 5, 'PAS FAIT', 14, 'PAS FAIT');

UPDATE bbc_ddb.chantier SET plomberie_elec_id = id;

INSERT INTO `bbc_ddb`.`user` (`id`, `username`, `roles`, `password`) VALUES 
(1, '<EMAIL>', '[]', '$2y$13$tKOFXQK9gua5Bx7cTK7JkemZRQQomj8r5kJxTbIlr/N52OIOwDN4S'),
(2, '<EMAIL>', '[]', '$2y$13$IWokwRMKPkLkNiq9RSXfKeM/1PWRs/dFxxpwRXNlu8Su/eWC33nGe'),
(3, '<EMAIL>', '[]', '$2y$13$JRypXf2uQyZiPeDNHCeisey1CANSJiWv1lLa9kZVN43hKLBKzUat6'),
(4, '<EMAIL>', '[]', '$2y$13$pnwlvqYNcFfnND51XjkYfeMVHgYUf23s1zG25QYSuk.UEgq4cYNey'),
(5, 'consultation', '["ROLE_WATCHER"]', '$2y$13$v3t2Cdn9Lo7wgvMnkKuhVu3xUPkYNOauIPsQPmliydCEm0Ewph2oO');