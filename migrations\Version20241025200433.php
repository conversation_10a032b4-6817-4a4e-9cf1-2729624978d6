<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025200433 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE terrassement (id INT AUTO_INCREMENT NOT NULL, terrassier_id INT DEFAULT NULL, branchement INT DEFAULT NULL, etat_branchement VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_6710C0B0362EEA7 (terrassier_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE terrassement ADD CONSTRAINT FK_6710C0B0362EEA7 FOREIGN KEY (terrassier_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE terrassement DROP FOREIGN KEY FK_6710C0B0362EEA7');
        $this->addSql('DROP TABLE terrassement');
    }
}
