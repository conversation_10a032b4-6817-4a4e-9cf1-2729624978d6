<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025202024 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE maconnerie (id INT AUTO_INCREMENT NOT NULL, macon_id INT DEFAULT NULL, fondation INT DEFAULT NULL, etat_fondation VARCHAR(255) DEFAULT NULL, dalle INT DEFAULT NULL, etat_dalle VARCHAR(255) DEFAULT NULL, elevation INT DEFAULT NULL, etat_elevation VARCHAR(255) DEFAULT NULL, rampannage INT DEFAULT NULL, etat_rampannage VARCHAR(255) DEFAULT NULL, releve_seuil INT DEFAULT NULL, etat_releve_seuil VARCHAR(255) DEFAULT NULL, seuil INT DEFAULT NULL, etat_seuil VARCHAR(255) DEFAULT NULL, terrasse INT DEFAULT NULL, etat_terrasse VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_6987B035D4F62C28 (macon_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE maconnerie ADD CONSTRAINT FK_6987B035D4F62C28 FOREIGN KEY (macon_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE maconnerie DROP FOREIGN KEY FK_6987B035D4F62C28');
        $this->addSql('DROP TABLE maconnerie');
    }
}
