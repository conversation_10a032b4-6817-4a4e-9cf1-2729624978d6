<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025202835 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE charpente (id INT AUTO_INCREMENT NOT NULL, charpentier_id INT DEFAULT NULL, charpente INT DEFAULT NULL, etat_charpente VARCHAR(255) DEFAULT NULL, couverture INT DEFAULT NULL, etat_couverture VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_2A2AD1AB7BAAC40B (charpentier_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE charpente ADD CONSTRAINT FK_2A2AD1AB7BAAC40B FOREIGN KEY (charpentier_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE charpente DROP FOREIGN KEY FK_2A2AD1AB7BAAC40B');
        $this->addSql('DROP TABLE charpente');
    }
}
