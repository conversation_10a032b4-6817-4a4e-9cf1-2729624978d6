<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025203834 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE etancheite (id INT AUTO_INCREMENT NOT NULL, etancheur_id INT DEFAULT NULL, intervention INT DEFAULT NULL, etat_intervention VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_A4943DC5F85BE9C7 (etancheur_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE etancheite ADD CONSTRAINT FK_A4943DC5F85BE9C7 FOREIGN KEY (etancheur_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE etancheite DROP FOREIGN KEY FK_A4943DC5F85BE9C7');
        $this->addSql('DROP TABLE etancheite');
    }
}
