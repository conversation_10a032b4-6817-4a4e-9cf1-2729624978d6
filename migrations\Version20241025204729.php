<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025204729 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE menuiserie (id INT AUTO_INCREMENT NOT NULL, menuisier_id INT DEFAULT NULL, pose_menuiserie INT DEFAULT NULL, etat_pose_menuiserie VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_E9B554E3AD8A8016 (menuisier_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE menuiserie ADD CONSTRAINT FK_E9B554E3AD8A8016 FOREIGN KEY (menuisier_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE menuiserie DROP FOREIGN KEY FK_E9B554E3AD8A8016');
        $this->addSql('DROP TABLE menuiserie');
    }
}
