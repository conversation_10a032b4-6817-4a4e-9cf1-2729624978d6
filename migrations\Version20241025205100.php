<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025205100 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE placo (id INT AUTO_INCREMENT NOT NULL, plaquiste_id INT DEFAULT NULL, prepa_placo INT DEFAULT NULL, etat_prepa_placo VARCHAR(255) DEFAULT NULL, fin_placo INT DEFAULT NULL, etat_fin_placo VARCHAR(255) DEFAULT NULL, cloison INT DEFAULT NULL, etat_cloison VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_94C8BAD3322E4F0D (plaquiste_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE placo ADD CONSTRAINT FK_94C8BAD3322E4F0D FOREIGN KEY (plaquiste_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE placo DROP FOREIGN KEY FK_94C8BAD3322E4F0D');
        $this->addSql('DROP TABLE placo');
    }
}
