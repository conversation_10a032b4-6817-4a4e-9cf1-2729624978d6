<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025205651 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE plomberie_elec (id INT AUTO_INCREMENT NOT NULL, plombier_electricien_id INT DEFAULT NULL, prepa_dalle INT DEFAULT NULL, etat_prepa_dalle VARCHAR(255) DEFAULT NULL, prepa_filerie_plomberie INT DEFAULT NULL, etat_prepa_filerie_plomberie VARCHAR(255) DEFAULT NULL, gros_oeuvre INT DEFAULT NULL, etat_gros_oeuvre VARCHAR(255) DEFAULT NULL, pose_pacchaudiere INT DEFAULT NULL, etat_pose_pacchaudiere VARCHAR(255) DEFAULT NULL, finition_elec INT DEFAULT NULL, etat_finition_elec VARCHAR(255) DEFAULT NULL, finition_plomberie_sanitaire INT DEFAULT NULL, etat_finition_plomberie_sanitaire VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_3ED8529AEDE69410 (plombier_electricien_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE plomberie_elec ADD CONSTRAINT FK_3ED8529AEDE69410 FOREIGN KEY (plombier_electricien_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE plomberie_elec DROP FOREIGN KEY FK_3ED8529AEDE69410');
        $this->addSql('DROP TABLE plomberie_elec');
    }
}
