<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025210354 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE enduit (id INT AUTO_INCREMENT NOT NULL, enduiseur_id INT DEFAULT NULL, intervention INT DEFAULT NULL, etat_intervention VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_CAD0594288A7B69C (enduiseur_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE enduit ADD CONSTRAINT FK_CAD0594288A7B69C FOREIGN KEY (enduiseur_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE enduit DROP FOREIGN KEY FK_CAD0594288A7B69C');
        $this->addSql('DROP TABLE enduit');
    }
}
