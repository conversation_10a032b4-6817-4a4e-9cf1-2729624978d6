<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025211151 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE carrelage (id INT AUTO_INCREMENT NOT NULL, carreleur_id INT DEFAULT NULL, pose_carrelage INT DEFAULT NULL, etat_pose_carrelage VARCHAR(255) DEFAULT NULL, pose_plinthe INT DEFAULT NULL, etat_pose_plinthe VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_DCE81D047C8A82B4 (carreleur_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE laine_souffle (id INT AUTO_INCREMENT NOT NULL, artisan_id INT DEFAULT NULL, intervention INT DEFAULT NULL, etat_intervention VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_E5180A085ED3C7B7 (artisan_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE carrelage ADD CONSTRAINT FK_DCE81D047C8A82B4 FOREIGN KEY (carreleur_id) REFERENCES artisan (id)');
        $this->addSql('ALTER TABLE laine_souffle ADD CONSTRAINT FK_E5180A085ED3C7B7 FOREIGN KEY (artisan_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE carrelage DROP FOREIGN KEY FK_DCE81D047C8A82B4');
        $this->addSql('ALTER TABLE laine_souffle DROP FOREIGN KEY FK_E5180A085ED3C7B7');
        $this->addSql('DROP TABLE carrelage');
        $this->addSql('DROP TABLE laine_souffle');
    }
}
