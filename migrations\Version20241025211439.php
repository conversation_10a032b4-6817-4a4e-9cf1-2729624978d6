<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025211439 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE escalier (id INT AUTO_INCREMENT NOT NULL, fabricant_id INT DEFAULT NULL, prise_cote INT DEFAULT NULL, etat_prise_cote VARCHAR(255) DEFAULT NULL, pose_escalier INT DEFAULT NULL, etat_pose_escalier VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_9AECAFEFCBAAAAB3 (fabricant_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE escalier ADD CONSTRAINT FK_9AECAFEFCBAAAAB3 FOREIGN KEY (fabricant_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE escalier DROP FOREIGN KEY FK_9AECAFEFCBAAAAB3');
        $this->addSql('DROP TABLE escalier');
    }
}
