<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025212035 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE finition (id INT AUTO_INCREMENT NOT NULL, artisan_id INT DEFAULT NULL, finition INT DEFAULT NULL, etat_finition VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_2060F8265ED3C7B7 (artisan_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE peinture (id INT AUTO_INCREMENT NOT NULL, peintre_id INT DEFAULT NULL, intervention INT DEFAULT NULL, etat_intervention VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_8FB3A9D691434246 (peintre_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE permeabilite (id INT AUTO_INCREMENT NOT NULL, artisan_id INT DEFAULT NULL, intervention INT DEFAULT NULL, etat_intervention VARCHAR(255) DEFAULT NULL, observation LONGTEXT DEFAULT NULL, INDEX IDX_5258D655ED3C7B7 (artisan_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE finition ADD CONSTRAINT FK_2060F8265ED3C7B7 FOREIGN KEY (artisan_id) REFERENCES artisan (id)');
        $this->addSql('ALTER TABLE peinture ADD CONSTRAINT FK_8FB3A9D691434246 FOREIGN KEY (peintre_id) REFERENCES artisan (id)');
        $this->addSql('ALTER TABLE permeabilite ADD CONSTRAINT FK_5258D655ED3C7B7 FOREIGN KEY (artisan_id) REFERENCES artisan (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE finition DROP FOREIGN KEY FK_2060F8265ED3C7B7');
        $this->addSql('ALTER TABLE peinture DROP FOREIGN KEY FK_8FB3A9D691434246');
        $this->addSql('ALTER TABLE permeabilite DROP FOREIGN KEY FK_5258D655ED3C7B7');
        $this->addSql('DROP TABLE finition');
        $this->addSql('DROP TABLE peinture');
        $this->addSql('DROP TABLE permeabilite');
    }
}
