<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241026102140 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE chantier ADD carrelage_id INT DEFAULT NULL, ADD chappe_id INT DEFAULT NULL, ADD charpente_id INT DEFAULT NULL, ADD enduit_id INT DEFAULT NULL, ADD escalier_id INT DEFAULT NULL, ADD etancheite_id INT DEFAULT NULL, ADD finition_id INT DEFAULT NULL, ADD laine_souffle_id INT DEFAULT NULL, ADD maconnerie_id INT DEFAULT NULL, ADD menuiserie_id INT DEFAULT NULL, ADD peinture_id INT DEFAULT NULL, ADD permeabilite_id INT DEFAULT NULL, ADD placo_id INT DEFAULT NULL, ADD plomberie_elec_id INT DEFAULT NULL, ADD terrassement_id INT DEFAULT NULL, ADD zingurie_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F630F56B66 FOREIGN KEY (carrelage_id) REFERENCES carrelage (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6CA20A38D FOREIGN KEY (chappe_id) REFERENCES chappe (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6F0F44A13 FOREIGN KEY (charpente_id) REFERENCES charpente (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6F8F5A89D FOREIGN KEY (enduit_id) REFERENCES enduit (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F644DC5C42 FOREIGN KEY (escalier_id) REFERENCES escalier (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F66CAF8EDC FOREIGN KEY (etancheite_id) REFERENCES etancheite (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6CB56F5AF FOREIGN KEY (finition_id) REFERENCES finition (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F681D1E4B1 FOREIGN KEY (laine_souffle_id) REFERENCES laine_souffle (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6D3F85A19 FOREIGN KEY (maconnerie_id) REFERENCES maconnerie (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6A8397473 FOREIGN KEY (menuiserie_id) REFERENCES menuiserie (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6C2E869AB FOREIGN KEY (peinture_id) REFERENCES peinture (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6ED74FEBF FOREIGN KEY (permeabilite_id) REFERENCES permeabilite (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6621B427D FOREIGN KEY (placo_id) REFERENCES placo (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F696409D18 FOREIGN KEY (plomberie_elec_id) REFERENCES plomberie_elec (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F67552FC6D FOREIGN KEY (terrassement_id) REFERENCES terrassement (id)');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F6ED173ADC FOREIGN KEY (zingurie_id) REFERENCES zinguerie (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F630F56B66 ON chantier (carrelage_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6CA20A38D ON chantier (chappe_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6F0F44A13 ON chantier (charpente_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6F8F5A89D ON chantier (enduit_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F644DC5C42 ON chantier (escalier_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F66CAF8EDC ON chantier (etancheite_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6CB56F5AF ON chantier (finition_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F681D1E4B1 ON chantier (laine_souffle_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6D3F85A19 ON chantier (maconnerie_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6A8397473 ON chantier (menuiserie_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6C2E869AB ON chantier (peinture_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6ED74FEBF ON chantier (permeabilite_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6621B427D ON chantier (placo_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F696409D18 ON chantier (plomberie_elec_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F67552FC6D ON chantier (terrassement_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F6ED173ADC ON chantier (zingurie_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F630F56B66');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6CA20A38D');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6F0F44A13');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6F8F5A89D');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F644DC5C42');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F66CAF8EDC');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6CB56F5AF');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F681D1E4B1');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6D3F85A19');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6A8397473');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6C2E869AB');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6ED74FEBF');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6621B427D');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F696409D18');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F67552FC6D');
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F6ED173ADC');
        $this->addSql('DROP INDEX UNIQ_636F27F630F56B66 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6CA20A38D ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6F0F44A13 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6F8F5A89D ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F644DC5C42 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F66CAF8EDC ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6CB56F5AF ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F681D1E4B1 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6D3F85A19 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6A8397473 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6C2E869AB ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6ED74FEBF ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6621B427D ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F696409D18 ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F67552FC6D ON chantier');
        $this->addSql('DROP INDEX UNIQ_636F27F6ED173ADC ON chantier');
        $this->addSql('ALTER TABLE chantier DROP carrelage_id, DROP chappe_id, DROP charpente_id, DROP enduit_id, DROP escalier_id, DROP etancheite_id, DROP finition_id, DROP laine_souffle_id, DROP maconnerie_id, DROP menuiserie_id, DROP peinture_id, DROP permeabilite_id, DROP placo_id, DROP plomberie_elec_id, DROP terrassement_id, DROP zingurie_id');
    }
}
