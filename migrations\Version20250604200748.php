<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250604200748 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE chantier ADD entretient_pac_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE chantier ADD CONSTRAINT FK_636F27F68E44068A FOREIGN KEY (entretient_pac_id) REFERENCES entretient_pac (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_636F27F68E44068A ON chantier (entretient_pac_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE chantier DROP FOREIGN KEY FK_636F27F68E44068A');
        $this->addSql('DROP INDEX UNIQ_636F27F68E44068A ON chantier');
        $this->addSql('ALTER TABLE chantier DROP entretient_pac_id');
    }
}
