/**
 * <PERSON>ript pour gérer le recalcul automatique des chantiers
 * lors de l'ajout/suppression de semaines interdites
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Fonction pour afficher une notification
    function showNotification(message, type = 'info') {
        // Vous pouvez adapter cette fonction selon votre système de notifications
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Fonction pour formater les résultats du recalcul
    function formatRecalculMessage(recalcul) {
        if (recalcul.chantiersModifies === 0) {
            return `${recalcul.chantiersRecalcules} chantier(s) vérifiés, aucune modification nécessaire.`;
        } else {
            return `${recalcul.chantiersModifies} chantier(s) recalculés avec ${recalcul.etapesTotalesModifiees} étape(s) modifiées.`;
        }
    }
    
    // Intercepter les réponses AJAX pour les semaines interdites
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .then(response => {
                // Vérifier si c'est une réponse d'ajout/suppression de semaine interdite
                const url = args[0];
                if (typeof url === 'string' && 
                    (url.includes('/semaines-interdites/ajouter') || 
                     url.includes('/semaines-interdites/supprimer'))) {
                    
                    return response.clone().json().then(data => {
                        if (data.success && data.recalcul) {
                            // Afficher les résultats du recalcul
                            const message = formatRecalculMessage(data.recalcul);
                            showNotification(`Semaine interdite mise à jour. ${message}`, 'success');
                            
                            // Si il y a des modifications importantes, afficher le modal détaillé
                            if (data.recalcul.chantiersModifies > 0 && 
                                typeof window.afficherModalRecalcul === 'function') {
                                setTimeout(() => {
                                    window.afficherModalRecalcul(data.recalcul);
                                }, 1000);
                            }
                            
                            // Recharger la page ou mettre à jour l'interface si nécessaire
                            if (data.recalcul.chantiersModifies > 0) {
                                // Vous pouvez ajouter ici du code pour mettre à jour l'interface
                                // sans recharger la page complète
                                console.log('Chantiers modifiés:', data.recalcul);
                            }
                        }
                        return response;
                    }).catch(() => response);
                }
                return response;
            });
    };
    
    // Fonction pour déclencher un recalcul manuel
    window.declencherRecalculManuel = function() {
        const btn = document.querySelector('[data-action="recalcul-manuel"]');
        if (btn) {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Recalcul en cours...';
        }
        
        fetch('/semaines-interdites/recalculer-chantiers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = formatRecalculMessage(data.resultats);
                showNotification(`Recalcul terminé. ${message}`, 'success');
                
                if (data.resultats.chantiersModifies > 0 && 
                    typeof window.afficherModalRecalcul === 'function') {
                    window.afficherModalRecalcul(data.resultats);
                }
            } else {
                showNotification(`Erreur lors du recalcul: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur de communication avec le serveur', 'error');
        })
        .finally(() => {
            if (btn) {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Recalculer';
            }
        });
    };
    
    // Ajouter un bouton de recalcul manuel si il n'existe pas
    function ajouterBoutonRecalculManuel() {
        const container = document.querySelector('.semaines-interdites-actions, .card-header, .page-header');
        if (container && !document.querySelector('[data-action="recalcul-manuel"]')) {
            const btn = document.createElement('button');
            btn.className = 'btn btn-outline-primary btn-sm ms-2';
            btn.setAttribute('data-action', 'recalcul-manuel');
            btn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>Recalculer les chantiers';
            btn.title = 'Recalculer tous les chantiers en cours avec les semaines interdites actuelles';
            btn.onclick = window.declencherRecalculManuel;
            
            container.appendChild(btn);
        }
    }
    
    // Ajouter le bouton après un court délai pour s'assurer que le DOM est prêt
    setTimeout(ajouterBoutonRecalculManuel, 500);
    
    // Fonction utilitaire pour vérifier si des chantiers sont affectés par une nouvelle semaine interdite
    window.verifierImpactSemaineInterdite = function(numeroSemaine, annee) {
        // Cette fonction pourrait être appelée avant d'ajouter une semaine interdite
        // pour informer l'utilisateur de l'impact potentiel
        
        return fetch('/semaines-interdites/api/verifier-impact', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                numeroSemaine: numeroSemaine,
                annee: annee
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.chantiersAffectes > 0) {
                return confirm(
                    `Cette semaine interdite affectera ${data.chantiersAffectes} chantier(s) en cours. ` +
                    `Voulez-vous continuer ?`
                );
            }
            return true;
        })
        .catch(error => {
            console.error('Erreur lors de la vérification:', error);
            return true; // Continuer en cas d'erreur
        });
    };
});

// Styles CSS pour les notifications (à ajouter dans votre CSS principal)
const styles = `
.notification-recalcul {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
}

.recalcul-details {
    font-size: 0.9em;
    margin-top: 0.5rem;
}

.recalcul-details .badge {
    margin-right: 0.25rem;
}
`;

// Injecter les styles
if (!document.getElementById('recalcul-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'recalcul-styles';
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);
}
