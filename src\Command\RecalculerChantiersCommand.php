<?php

namespace App\Command;

use App\Service\ChantierRecalculService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:recalculer-chantiers',
    description: 'Recalcule tous les chantiers en cours en tenant compte des semaines interdites',
)]
class RecalculerChantiersCommand extends Command
{
    private ChantierRecalculService $chantierRecalculService;

    public function __construct(ChantierRecalculService $chantierRecalculService)
    {
        $this->chantierRecalculService = $chantierRecalculService;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Affiche les modifications sans les sauvegarder')
            ->addOption('details', 'd', InputOption::VALUE_NONE, 'Affiche les détails des modifications')
            ->setHelp('
Cette commande recalcule tous les chantiers en cours en tenant compte des semaines interdites actuelles.

Exemples d\'utilisation :
  <info>php bin/console app:recalculer-chantiers</info>                    # Recalcule et sauvegarde
  <info>php bin/console app:recalculer-chantiers --dry-run</info>          # Simulation sans sauvegarde
  <info>php bin/console app:recalculer-chantiers --details</info>          # Avec détails des modifications
            ');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');
        $details = $input->getOption('details');

        $io->title('Recalcul des chantiers en cours');

        if ($dryRun) {
            $io->note('Mode simulation activé - aucune modification ne sera sauvegardée');
        }

        try {
            $startTime = microtime(true);
            
            // Effectuer le recalcul
            $resultats = $this->chantierRecalculService->recalculerTousLesChantiersEnCours();
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            // Afficher les résultats
            $io->section('Résultats du recalcul');
            
            $io->definitionList(
                ['Chantiers vérifiés' => $resultats['chantiersRecalcules']],
                ['Chantiers modifiés' => $resultats['chantiersModifies']],
                ['Étapes recalculées' => $resultats['etapesTotalesModifiees']],
                ['Temps d\'exécution' => $executionTime . ' secondes']
            );

            if ($resultats['chantiersModifies'] === 0) {
                $io->success('Aucun chantier n\'a nécessité de modification.');
                return Command::SUCCESS;
            }

            // Afficher les détails si demandé
            if ($details && !empty($resultats['details'])) {
                $io->section('Détails des modifications');
                
                foreach ($resultats['details'] as $detail) {
                    $io->text(sprintf(
                        '<info>Chantier #%d</info> - %s (%s)',
                        $detail['chantierId'],
                        $detail['client'],
                        $detail['commune']
                    ));
                    
                    foreach ($detail['etapesModifiees'] as $etape => $modification) {
                        $io->text(sprintf(
                            '  • %s: Semaine %d → Semaine %d (Année %d)',
                            $etape,
                            $modification['ancienne'],
                            $modification['nouvelle'],
                            $modification['annee']
                        ));
                        
                        if (!empty($modification['semainesEvitees'])) {
                            $io->text(sprintf(
                                '    Semaines évitées: %s',
                                implode(', ', $modification['semainesEvitees'])
                            ));
                        }
                    }
                    $io->newLine();
                }
            }

            if ($dryRun) {
                $io->warning('Mode simulation - Les modifications ci-dessus n\'ont PAS été sauvegardées.');
                $io->note('Relancez la commande sans --dry-run pour appliquer les modifications.');
            } else {
                $io->success(sprintf(
                    'Recalcul terminé avec succès ! %d chantier(s) ont été modifiés.',
                    $resultats['chantiersModifies']
                ));
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Erreur lors du recalcul des chantiers: ' . $e->getMessage());

            if ($details) {
                $io->text('Trace complète:');
                $io->text($e->getTraceAsString());
            }
            
            return Command::FAILURE;
        }
    }
}
