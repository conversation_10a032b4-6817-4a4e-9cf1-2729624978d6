<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Entity\Artisan;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Repository\ArtisanRepository;

class ArtisanController extends AbstractController
{

    #[Route('/get_artisans', name: 'get_artisans')]
    public function getArtisans(Request $request, ArtisanRepository $artisanRepository): Response
    {
        $metier = $request->request->get('metier');
        $artisans = $artisanRepository->findByType($metier);
        $response = [];
        foreach ($artisans as $artisan) {
            $response[] = $artisan->getArtisanJson();
        }
        return $this->json($response);
    }

    #[Route('/artisans', name: 'list_artisans')]
    public function listArtisans(ArtisanRepository $artisanRepository): Response
    {
        $artisans = $artisanRepository->findAll();
        $groupedArtisans = [];
        foreach ($artisans as $artisan) {
            $type = $artisan->getType();
            if (!isset($groupedArtisans[$type])) {
            $groupedArtisans[$type] = [];
            }
            $groupedArtisans[$type][] = $artisan;
        }

        foreach ($groupedArtisans as &$group) {
            usort($group, function ($a, $b) {
            return strcmp($a->getNom(), $b->getNom());
            });
        }

        return $this->render('artisan/index.html.twig', [
            'groupedArtisans' => $groupedArtisans,
        ]);
    }

    #[Route('/add_artisan', name: 'add_artisan')]
    public function addArtisan(Request $request, EntityManagerInterface $em): Response
    {
        $artisan = new Artisan();
        $artisan->setNom($request->request->get('nom'));
        $artisan->setType($request->request->get('type'));
        $em->persist($artisan);
        $em->flush();
        return $this->redirectToRoute('list_artisans');
    }

    #[Route('/edit_artisan/{id}', name: 'edit_artisan')]
    public function editArtisan($id, Request $request, EntityManagerInterface $em, ArtisanRepository $artisanRepository): Response
    {
        $artisan = $artisanRepository->find($id);
        $artisan->setNom($request->request->get('nom'));
        $em->persist($artisan);
        $em->flush();
        return $this->json(['message' => 'Artisan modifié']);
    }

    #[Route('/delete_artisan/{id}', name: 'delete_artisan')]
    public function deleteArtisan($id, EntityManagerInterface $em, ArtisanRepository $artisanRepository): Response
    {
        $artisan = $artisanRepository->find($id);
        $artisan->removeAll();
        $em->remove($artisan);
        $em->flush();
        return $this->json(['message' => 'Artisan supprimé']);
    }
}
