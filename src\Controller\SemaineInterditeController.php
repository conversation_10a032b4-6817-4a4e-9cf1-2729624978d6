<?php

namespace App\Controller;

use App\Entity\SemaineInterdite;
use App\Repository\SemaineInterditeRepository;
use App\Service\ChantierRecalculService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class SemaineInterditeController extends AbstractController
{
    #[Route('/semaines-interdites', name: 'app_semaines_interdites')]
    #[IsGranted('ROLE_USER')]
    public function index(SemaineInterditeRepository $repository): Response
    {
        $anneeCourante = (int) date('Y');
        $semaines = $repository->findAll();
        
        // Grouper par année
        $semainesParAnnee = [];
        foreach ($semaines as $semaine) {
            $semainesParAnnee[$semaine->getAnnee()][] = $semaine;
        }

        return $this->render('semaine_interdite/index.html.twig', [
            'semainesParAnnee' => $semainesParAnnee,
            'anneeCourante' => $anneeCourante,
        ]);
    }

    #[Route('/semaines-interdites/ajouter', name: 'app_semaines_interdites_add', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function ajouter(Request $request, EntityManagerInterface $em, SemaineInterditeRepository $repository, ChantierRecalculService $chantierRecalculService): JsonResponse
    {
        $numeroSemaine = (int) $request->request->get('numeroSemaine');
        $annee = (int) $request->request->get('annee');
        $motif = $request->request->get('motif');

        // Validations
        if ($numeroSemaine < 1 || $numeroSemaine > 53) {
            return new JsonResponse(['error' => 'Numéro de semaine invalide (1-53)'], 400);
        }

        if ($annee < date('Y') || $annee > date('Y') + 5) {
            return new JsonResponse(['error' => 'Année invalide'], 400);
        }

        // Vérifier si la semaine existe déjà
        $existante = $repository->findBySemaineEtAnnee($numeroSemaine, $annee);
        if ($existante) {
            return new JsonResponse(['error' => 'Cette semaine est déjà interdite'], 400);
        }

        // Créer la nouvelle semaine interdite
        $semaineInterdite = new SemaineInterdite();
        $semaineInterdite->setNumeroSemaine($numeroSemaine);
        $semaineInterdite->setAnnee($annee);
        $semaineInterdite->setMotif($motif);
        $semaineInterdite->setCreePar($this->getUser()->getUserIdentifier());

        $em->persist($semaineInterdite);
        $em->flush();

        // Recalculer tous les chantiers en cours après ajout de la semaine interdite
        $resultatsRecalcul = $chantierRecalculService->recalculerTousLesChantiersEnCours();

        return new JsonResponse([
            'success' => true,
            'message' => 'Semaine interdite ajoutée avec succès',
            'semaine' => [
                'id' => $semaineInterdite->getId(),
                'numeroSemaine' => $semaineInterdite->getNumeroSemaine(),
                'annee' => $semaineInterdite->getAnnee(),
                'motif' => $semaineInterdite->getMotif(),
                'semaineFormatee' => $semaineInterdite->getSemaineFormatee(),
                'dates' => $semaineInterdite->getDatesDebutFin()
            ],
            'recalcul' => [
                'chantiersRecalcules' => $resultatsRecalcul['chantiersRecalcules'],
                'chantiersModifies' => $resultatsRecalcul['chantiersModifies'],
                'etapesTotalesModifiees' => $resultatsRecalcul['etapesTotalesModifiees']
            ]
        ]);
    }

    #[Route('/semaines-interdites/modifier/{id}', name: 'app_semaines_interdites_edit', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function modifier(int $id, Request $request, EntityManagerInterface $em, SemaineInterditeRepository $repository): JsonResponse
    {
        $semaineInterdite = $repository->find($id);
        if (!$semaineInterdite) {
            return new JsonResponse(['error' => 'Semaine interdite non trouvée'], 404);
        }

        $motif = $request->request->get('motif');
        $semaineInterdite->setMotif($motif);

        $em->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Semaine interdite modifiée avec succès'
        ]);
    }

    #[Route('/semaines-interdites/supprimer/{id}', name: 'app_semaines_interdites_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_USER')]
    public function supprimer(int $id, EntityManagerInterface $em, SemaineInterditeRepository $repository, ChantierRecalculService $chantierRecalculService): JsonResponse
    {
        $semaineInterdite = $repository->find($id);
        if (!$semaineInterdite) {
            return new JsonResponse(['error' => 'Semaine interdite non trouvée'], 404);
        }

        $em->remove($semaineInterdite);
        $em->flush();

        // Recalculer tous les chantiers en cours après suppression de la semaine interdite
        $resultatsRecalcul = $chantierRecalculService->recalculerTousLesChantiersEnCours();

        return new JsonResponse([
            'success' => true,
            'message' => 'Semaine interdite supprimée avec succès',
            'recalcul' => [
                'chantiersRecalcules' => $resultatsRecalcul['chantiersRecalcules'],
                'chantiersModifies' => $resultatsRecalcul['chantiersModifies'],
                'etapesTotalesModifiees' => $resultatsRecalcul['etapesTotalesModifiees']
            ]
        ]);
    }

    #[Route('/semaines-interdites/api/liste', name: 'app_semaines_interdites_api_liste', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function apiListe(SemaineInterditeRepository $repository): JsonResponse
    {
        $semaines = $repository->getSemainesInterditesPourCalcul();
        
        return new JsonResponse($semaines);
    }

    #[Route('/semaines-interdites/api/verifier', name: 'app_semaines_interdites_api_verifier', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function apiVerifier(Request $request, SemaineInterditeRepository $repository): JsonResponse
    {
        $numeroSemaine = (int) $request->request->get('numeroSemaine');
        $annee = (int) $request->request->get('annee');

        $estInterdite = $repository->isSemaineInterdite($numeroSemaine, $annee);

        return new JsonResponse([
            'estInterdite' => $estInterdite,
            'semaine' => $numeroSemaine,
            'annee' => $annee
        ]);
    }

    #[Route('/semaines-interdites/api/calculer-avec-interdites', name: 'app_semaines_interdites_api_calculer', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function apiCalculerAvecInterdites(Request $request, SemaineInterditeRepository $repository): JsonResponse
    {
        $semaineDebut = (int) $request->request->get('semaineDebut');
        $anneeDebut = (int) $request->request->get('anneeDebut');
        $decalage = (int) $request->request->get('decalage');

        $semainesInterdites = $repository->getSemainesInterditesPourCalcul();
        
        $semaineCalculee = $semaineDebut;
        $anneeCalculee = $anneeDebut;
        
        // Appliquer le décalage en évitant les semaines interdites
        for ($i = 0; $i < $decalage; $i++) {
            do {
                $semaineCalculee++;
                if ($semaineCalculee > 52) {
                    $semaineCalculee = 1;
                    $anneeCalculee++;
                }
                
                $key = $anneeCalculee . '-' . str_pad($semaineCalculee, 2, '0', STR_PAD_LEFT);
            } while (isset($semainesInterdites[$key]));
        }

        return new JsonResponse([
            'semaineOriginale' => $semaineDebut + $decalage > 52 ? ($semaineDebut + $decalage) % 52 : $semaineDebut + $decalage,
            'semaineAvecInterdites' => $semaineCalculee,
            'anneeAvecInterdites' => $anneeCalculee,
            'semainesEvitees' => array_keys($semainesInterdites)
        ]);
    }

    #[Route('/semaines-interdites/recalculer-chantiers', name: 'app_semaines_interdites_recalculer_chantiers', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function recalculerChantiers(ChantierRecalculService $chantierRecalculService): JsonResponse
    {
        try {
            $resultats = $chantierRecalculService->recalculerTousLesChantiersEnCours();

            return new JsonResponse([
                'success' => true,
                'message' => 'Recalcul des chantiers terminé avec succès',
                'resultats' => $resultats
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du recalcul des chantiers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
