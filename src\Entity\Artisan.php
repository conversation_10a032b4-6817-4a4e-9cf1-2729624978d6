<?php

namespace App\Entity;

use App\Repository\ArtisanRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ArtisanRepository::class)]
class Artisan
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $Nom = null;

    #[ORM\Column(length: 255)]
    private ?string $Type = null;

    /**
     * @var Collection<int, Terrassement>
     */
    #[ORM\OneToMany(targetEntity: Terrassement::class, mappedBy: 'Terrassier')]
    private Collection $terrassements;

    /**
     * @var Collection<int, Maconnerie>
     */
    #[ORM\OneToMany(targetEntity: Maconnerie::class, mappedBy: 'Macon')]
    private Collection $maconneries;

    /**
     * @var Collection<int, Charpente>
     */
    #[ORM\OneToMany(targetEntity: Charpente::class, mappedBy: 'Charpentier')]
    private Collection $charpentes;

    /**
     * @var Collection<int, Etancheite>
     */
    #[ORM\OneToMany(targetEntity: Etancheite::class, mappedBy: 'Etancheur')]
    private Collection $etancheites;

    /**
     * @var Collection<int, Zinguerie>
     */
    #[ORM\OneToMany(targetEntity: Zinguerie::class, mappedBy: 'Artisan')]
    private Collection $zingueries;

    /**
     * @var Collection<int, Menuiserie>
     */
    #[ORM\OneToMany(targetEntity: Menuiserie::class, mappedBy: 'Menuisier')]
    private Collection $menuiseries;

    /**
     * @var Collection<int, Placo>
     */
    #[ORM\OneToMany(targetEntity: Placo::class, mappedBy: 'Plaquiste')]
    private Collection $placos;

    /**
     * @var Collection<int, PlomberieElec>
     */
    #[ORM\OneToMany(targetEntity: PlomberieElec::class, mappedBy: 'PlombierElectricien')]
    private Collection $plomberieElecs;

    /**
     * @var Collection<int, Chappe>
     */
    #[ORM\OneToMany(targetEntity: Chappe::class, mappedBy: 'Artisan')]
    private Collection $chappes;

    /**
     * @var Collection<int, Enduit>
     */
    #[ORM\OneToMany(targetEntity: Enduit::class, mappedBy: 'Enduiseur')]
    private Collection $enduits;

    /**
     * @var Collection<int, LaineSouffle>
     */
    #[ORM\OneToMany(targetEntity: LaineSouffle::class, mappedBy: 'Artisan')]
    private Collection $laineSouffles;

    /**
     * @var Collection<int, Carrelage>
     */
    #[ORM\OneToMany(targetEntity: Carrelage::class, mappedBy: 'Carreleur')]
    private Collection $carrelages;

    /**
     * @var Collection<int, Escalier>
     */
    #[ORM\OneToMany(targetEntity: Escalier::class, mappedBy: 'Fabricant')]
    private Collection $escaliers;

    /**
     * @var Collection<int, Finition>
     */
    #[ORM\OneToMany(targetEntity: Finition::class, mappedBy: 'Artisan')]
    private Collection $finitions;

    /**
     * @var Collection<int, Peinture>
     */
    #[ORM\OneToMany(targetEntity: Peinture::class, mappedBy: 'Peintre')]
    private Collection $peintures;

    /**
     * @var Collection<int, Permeabilite>
     */
    #[ORM\OneToMany(targetEntity: Permeabilite::class, mappedBy: 'Artisan')]
    private Collection $permeabilites;

    #[ORM\OneToMany(targetEntity: EntretientPAC::class, mappedBy: 'Artisan')]
    private Collection $entretientPACs;

    public function __construct()
    {
        $this->terrassements = new ArrayCollection();
        $this->maconneries = new ArrayCollection();
        $this->charpentes = new ArrayCollection();
        $this->etancheites = new ArrayCollection();
        $this->zingueries = new ArrayCollection();
        $this->menuiseries = new ArrayCollection();
        $this->placos = new ArrayCollection();
        $this->plomberieElecs = new ArrayCollection();
        $this->chappes = new ArrayCollection();
        $this->enduits = new ArrayCollection();
        $this->laineSouffles = new ArrayCollection();
        $this->carrelages = new ArrayCollection();
        $this->escaliers = new ArrayCollection();
        $this->finitions = new ArrayCollection();
        $this->peintures = new ArrayCollection();
        $this->permeabilites = new ArrayCollection();
        $this->entretientPACs = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNom(): ?string
    {
        return $this->Nom;
    }

    public function setNom(string $Nom): static
    {
        $this->Nom = $Nom;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->Type;
    }

    public function setType(string $Type): static
    {
        $this->Type = $Type;

        return $this;
    }

    /**
     * @return Collection<int, Terrassement>
     */
    public function getTerrassements(): Collection
    {
        return $this->terrassements;
    }

    public function addTerrassement(Terrassement $terrassement): static
    {
        if (!$this->terrassements->contains($terrassement)) {
            $this->terrassements->add($terrassement);
            $terrassement->setTerrassier($this);
        }

        return $this;
    }

    public function removeTerrassement(Terrassement $terrassement): static
    {
        if ($this->terrassements->removeElement($terrassement)) {
            // set the owning side to null (unless already changed)
            if ($terrassement->getTerrassier() === $this) {
                $terrassement->setTerrassier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Maconnerie>
     */
    public function getMaconneries(): Collection
    {
        return $this->maconneries;
    }

    public function addMaconnery(Maconnerie $maconnery): static
    {
        if (!$this->maconneries->contains($maconnery)) {
            $this->maconneries->add($maconnery);
            $maconnery->setMacon($this);
        }

        return $this;
    }

    public function removeMaconnery(Maconnerie $maconnery): static
    {
        if ($this->maconneries->removeElement($maconnery)) {
            // set the owning side to null (unless already changed)
            if ($maconnery->getMacon() === $this) {
                $maconnery->setMacon(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Charpente>
     */
    public function getCharpentes(): Collection
    {
        return $this->charpentes;
    }

    public function addCharpente(Charpente $charpente): static
    {
        if (!$this->charpentes->contains($charpente)) {
            $this->charpentes->add($charpente);
            $charpente->setCharpentier($this);
        }

        return $this;
    }

    public function removeCharpente(Charpente $charpente): static
    {
        if ($this->charpentes->removeElement($charpente)) {
            // set the owning side to null (unless already changed)
            if ($charpente->getCharpentier() === $this) {
                $charpente->setCharpentier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Etancheite>
     */
    public function getEtancheites(): Collection
    {
        return $this->etancheites;
    }

    public function addEtancheite(Etancheite $etancheite): static
    {
        if (!$this->etancheites->contains($etancheite)) {
            $this->etancheites->add($etancheite);
            $etancheite->setEtancheur($this);
        }

        return $this;
    }

    public function removeEtancheite(Etancheite $etancheite): static
    {
        if ($this->etancheites->removeElement($etancheite)) {
            // set the owning side to null (unless already changed)
            if ($etancheite->getEtancheur() === $this) {
                $etancheite->setEtancheur(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Zinguerie>
     */
    public function getZingueries(): Collection
    {
        return $this->zingueries;
    }

    public function addZinguery(Zinguerie $zinguery): static
    {
        if (!$this->zingueries->contains($zinguery)) {
            $this->zingueries->add($zinguery);
            $zinguery->setArtisan($this);
        }

        return $this;
    }

    public function removeZinguery(Zinguerie $zinguery): static
    {
        if ($this->zingueries->removeElement($zinguery)) {
            // set the owning side to null (unless already changed)
            if ($zinguery->getArtisan() === $this) {
                $zinguery->setArtisan(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Menuiserie>
     */
    public function getMenuiseries(): Collection
    {
        return $this->menuiseries;
    }

    public function addMenuiseries(Menuiserie $menuiseries): static
    {
        if (!$this->menuiseries->contains($menuiseries)) {
            $this->menuiseries->add($menuiseries);
            $menuiseries->setMenuisier($this);
        }

        return $this;
    }

    public function removeMenuiseries(Menuiserie $menuiseries): static
    {
        if ($this->menuiseries->removeElement($menuiseries)) {
            // set the owning side to null (unless already changed)
            if ($menuiseries->getMenuisier() === $this) {
                $menuiseries->setMenuisier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Placo>
     */
    public function getPlacos(): Collection
    {
        return $this->placos;
    }

    public function addPlaco(Placo $placo): static
    {
        if (!$this->placos->contains($placo)) {
            $this->placos->add($placo);
            $placo->setPlaquiste($this);
        }

        return $this;
    }

    public function removePlaco(Placo $placo): static
    {
        if ($this->placos->removeElement($placo)) {
            // set the owning side to null (unless already changed)
            if ($placo->getPlaquiste() === $this) {
                $placo->setPlaquiste(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlomberieElec>
     */
    public function getPlomberieElecs(): Collection
    {
        return $this->plomberieElecs;
    }

    public function addPlomberieElec(PlomberieElec $plomberieElec): static
    {
        if (!$this->plomberieElecs->contains($plomberieElec)) {
            $this->plomberieElecs->add($plomberieElec);
            $plomberieElec->setPlombierElectricien($this);
        }

        return $this;
    }

    public function removePlomberieElec(PlomberieElec $plomberieElec): static
    {
        if ($this->plomberieElecs->removeElement($plomberieElec)) {
            // set the owning side to null (unless already changed)
            if ($plomberieElec->getPlombierElectricien() === $this) {
                $plomberieElec->setPlombierElectricien(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Chappe>
     */
    public function getChappes(): Collection
    {
        return $this->chappes;
    }

    public function addChappe(Chappe $chappe): static
    {
        if (!$this->chappes->contains($chappe)) {
            $this->chappes->add($chappe);
            $chappe->setArtisan($this);
        }

        return $this;
    }

    public function removeChappe(Chappe $chappe): static
    {
        if ($this->chappes->removeElement($chappe)) {
            // set the owning side to null (unless already changed)
            if ($chappe->getArtisan() === $this) {
                $chappe->setArtisan(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Enduit>
     */
    public function getEnduits(): Collection
    {
        return $this->enduits;
    }

    public function addEnduit(Enduit $enduit): static
    {
        if (!$this->enduits->contains($enduit)) {
            $this->enduits->add($enduit);
            $enduit->setEnduiseur($this);
        }

        return $this;
    }

    public function removeEnduit(Enduit $enduit): static
    {
        if ($this->enduits->removeElement($enduit)) {
            // set the owning side to null (unless already changed)
            if ($enduit->getEnduiseur() === $this) {
                $enduit->setEnduiseur(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, LaineSouffle>
     */
    public function getLaineSouffles(): Collection
    {
        return $this->laineSouffles;
    }

    public function addLaineSouffle(LaineSouffle $laineSouffle): static
    {
        if (!$this->laineSouffles->contains($laineSouffle)) {
            $this->laineSouffles->add($laineSouffle);
            $laineSouffle->setArtisan($this);
        }

        return $this;
    }

    public function removeLaineSouffle(LaineSouffle $laineSouffle): static
    {
        if ($this->laineSouffles->removeElement($laineSouffle)) {
            // set the owning side to null (unless already changed)
            if ($laineSouffle->getArtisan() === $this) {
                $laineSouffle->setArtisan(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Carrelage>
     */
    public function getCarrelages(): Collection
    {
        return $this->carrelages;
    }

    public function addCarrelage(Carrelage $carrelage): static
    {
        if (!$this->carrelages->contains($carrelage)) {
            $this->carrelages->add($carrelage);
            $carrelage->setCarreleur($this);
        }

        return $this;
    }

    public function removeCarrelage(Carrelage $carrelage): static
    {
        if ($this->carrelages->removeElement($carrelage)) {
            // set the owning side to null (unless already changed)
            if ($carrelage->getCarreleur() === $this) {
                $carrelage->setCarreleur(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Escalier>
     */
    public function getEscaliers(): Collection
    {
        return $this->escaliers;
    }

    public function addEscalier(Escalier $escalier): static
    {
        if (!$this->escaliers->contains($escalier)) {
            $this->escaliers->add($escalier);
            $escalier->setFabricant($this);
        }

        return $this;
    }

    public function removeEscalier(Escalier $escalier): static
    {
        if ($this->escaliers->removeElement($escalier)) {
            // set the owning side to null (unless already changed)
            if ($escalier->getFabricant() === $this) {
                $escalier->setFabricant(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Finition>
     */
    public function getFinitions(): Collection
    {
        return $this->finitions;
    }

    public function addFinition(Finition $finition): static
    {
        if (!$this->finitions->contains($finition)) {
            $this->finitions->add($finition);
            $finition->setArtisan($this);
        }

        return $this;
    }

    public function removeFinition(Finition $finition): static
    {
        if ($this->finitions->removeElement($finition)) {
            // set the owning side to null (unless already changed)
            if ($finition->getArtisan() === $this) {
                $finition->setArtisan(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Peinture>
     */
    public function getPeintures(): Collection
    {
        return $this->peintures;
    }

    public function addPeinture(Peinture $peinture): static
    {
        if (!$this->peintures->contains($peinture)) {
            $this->peintures->add($peinture);
            $peinture->setPeintre($this);
        }

        return $this;
    }

    public function removePeinture(Peinture $peinture): static
    {
        if ($this->peintures->removeElement($peinture)) {
            // set the owning side to null (unless already changed)
            if ($peinture->getPeintre() === $this) {
                $peinture->setPeintre(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Permeabilite>
     */
    public function getPermeabilites(): Collection
    {
        return $this->permeabilites;
    }

    public function addPermeabilite(Permeabilite $permeabilite): static
    {
        if (!$this->permeabilites->contains($permeabilite)) {
            $this->permeabilites->add($permeabilite);
            $permeabilite->setArtisan($this);
        }

        return $this;
    }

    public function removePermeabilite(Permeabilite $permeabilite): static
    {
        if ($this->permeabilites->removeElement($permeabilite)) {
            // set the owning side to null (unless already changed)
            if ($permeabilite->getArtisan() === $this) {
                $permeabilite->setArtisan(null);
            }
        }

        return $this;
    }

    public function getEntretientPACs(): Collection
    {
        return $this->entretientPACs;
    }

    public function addEntretientPAC(EntretientPAC $entretientPAC): static
    {
        if (!$this->entretientPACs->contains($entretientPAC)) {
            $this->entretientPACs->add($entretientPAC);
            $entretientPAC->setArtisan($this);
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->Nom;
    }

    public function removeAll(): static
    {
        foreach ($this->terrassements as $terrassement) {
            $this->removeTerrassement($terrassement);
        }
        foreach ($this->maconneries as $maconnery) {
            $this->removeMaconnery($maconnery);
        }
        foreach ($this->charpentes as $charpente) {
            $this->removeCharpente($charpente);
        }
        foreach ($this->etancheites as $etancheite) {
            $this->removeEtancheite($etancheite);
        }
        foreach ($this->zingueries as $zinguery) {
            $this->removeZinguery($zinguery);
        }
        foreach ($this->menuiseries as $menuiseries) {
            $this->removeMenuiseries($menuiseries);
        }
        foreach ($this->placos as $placo) {
            $this->removePlaco($placo);
        }
        foreach ($this->plomberieElecs as $plomberieElec) {
            $this->removePlomberieElec($plomberieElec);
        }
        foreach ($this->chappes as $chappe) {
            $this->removeChappe($chappe);
        }
        foreach ($this->enduits as $enduit) {
            $this->removeEnduit($enduit);
        }
        foreach ($this->laineSouffles as $laineSouffle) {
            $this->removeLaineSouffle($laineSouffle);
        }
        foreach ($this->carrelages as $carrelage) {
            $this->removeCarrelage($carrelage);
        }
        foreach ($this->escaliers as $escalier) {
            $this->removeEscalier($escalier);
        }
        foreach ($this->finitions as $finition) {
            $this->removeFinition($finition);
        }
        foreach ($this->peintures as $peinture) {
            $this->removePeinture($peinture);
        }
        foreach ($this->permeabilites as $permeabilite) {
            $this->removePermeabilite($permeabilite);
        }
        foreach ($this->entretientPACs as $entretientPAC) {
            $this->removeEntretientPAC($entretientPAC);
        }

        return $this;
    }

    public function getArtisanJson(): array
    {
        return [
            'id' => $this->id,
            'Nom' => $this->Nom,
            'Type' => $this->Type,
        ];
    }

    public function getNbChantiers(): int
    {
        return count($this->terrassements) + count($this->maconneries) + count($this->charpentes) + count($this->etancheites) + count($this->zingueries) + count($this->menuiseries) + count($this->placos) + count($this->plomberieElecs) + count($this->chappes) + count($this->enduits) + count($this->laineSouffles) + count($this->carrelages) + count($this->escaliers) + count($this->finitions) + count($this->peintures) + count($this->permeabilites) + count($this->entretientPACs);
    }
}
