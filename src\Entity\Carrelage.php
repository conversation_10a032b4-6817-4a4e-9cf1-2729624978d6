<?php

namespace App\Entity;

use App\Repository\CarrelageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CarrelageRepository::class)]
class Carrelage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'carrelages')]
    private ?Artisan $Carreleur = null;

    #[ORM\Column(nullable: true)]
    private ?int $PoseCarrelage = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPoseCarrelage = null;

    #[ORM\Column(nullable: true)]
    private ?int $PosePlinthe = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPosePlinthe = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Carrelage', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCarreleur(): ?Artisan
    {
        return $this->Carreleur;
    }

    public function setCarreleur(?Artisan $Carreleur): static
    {
        $this->Carreleur = $Carreleur;

        return $this;
    }

    public function getPoseCarrelage(): ?int
    {
        return $this->PoseCarrelage;
    }

    public function setPoseCarrelage(?int $PoseCarrelage): static
    {
        $this->PoseCarrelage = $PoseCarrelage;

        return $this;
    }

    public function getEtatPoseCarrelage(): ?string
    {
        return $this->EtatPoseCarrelage;
    }

    public function setEtatPoseCarrelage(?string $EtatPoseCarrelage): static
    {
        $this->EtatPoseCarrelage = $EtatPoseCarrelage;

        return $this;
    }

    public function getPosePlinthe(): ?int
    {
        return $this->PosePlinthe;
    }

    public function setPosePlinthe(?int $PosePlinthe): static
    {
        $this->PosePlinthe = $PosePlinthe;

        return $this;
    }

    public function getEtatPosePlinthe(): ?string
    {
        return $this->EtatPosePlinthe;
    }

    public function setEtatPosePlinthe(?string $EtatPosePlinthe): static
    {
        $this->EtatPosePlinthe = $EtatPosePlinthe;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setCarrelage(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getCarrelage() !== $this) {
            $chantier->setCarrelage($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Carreleur;
    }

    public function setArtisan(?Artisan $Carreleur): static
    {
        $this->Carreleur = $Carreleur;

        return $this;
    }

    
    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Carreleur?->getId(),
            ],
            'Pose Carrelage' => [
                'nomAttribut' => 'PoseCarrelage',
                'week' => $this->PoseCarrelage,
                'etat' => $this->EtatPoseCarrelage
            ],
            'Pose Plinthe' => [
                'nomAttribut' => 'PosePlinthe',
                'week' => $this->PosePlinthe,
                'etat' => $this->EtatPosePlinthe
            ],
            'Observation' => $this->Observation,
        ];
    }
}
