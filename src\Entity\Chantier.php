<?php

namespace App\Entity;

use App\Repository\ChantierRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ChantierRepository::class)]
class Chantier
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $Client = null;

    #[ORM\Column(length: 255)]
    private ?string $Commune = null;

    #[ORM\Column(length: 255)]
    private ?string $CDT = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $DateDeb = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $DateFin = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $dateReel = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Carrelage $Carrelage = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Chappe $Chappe = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Charpente $Charpente = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Enduit $Enduit = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Escalier $Escalier = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Etancheite $Etancheite = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Finition $Finition = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?LaineSouffle $LaineSouffle = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Maconnerie $Maconnerie = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Menuiserie $Menuiserie = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Peinture $Peinture = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Permeabilite $Permeabilite = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Placo $Placo = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?PlomberieElec $PlomberieElec = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Terrassement $Terrassement = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?Zinguerie $Zingurie = null;

    #[ORM\OneToOne(inversedBy: 'chantier', cascade: ['persist', 'remove'])]
    private ?EntretientPAC $EntretientPAC = null;

    #[ORM\Column(nullable: true)]
    private ?bool $archive = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $mouvements = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClient(): ?string
    {
        return $this->Client;
    }

    public function setClient(string $Client): static
    {
        $this->Client = $Client;

        return $this;
    }

    public function getCommune(): ?string
    {
        return $this->Commune;
    }

    public function setCommune(string $Commune): static
    {
        $this->Commune = $Commune;

        return $this;
    }

    public function getCDT(): ?string
    {
        return $this->CDT;
    }

    public function setCDT(string $CDT): static
    {
        $this->CDT = $CDT;

        return $this;
    }

    public function getDateDeb(): ?\DateTimeInterface
    {
        return $this->DateDeb;
    }

    public function setDateDeb(?\DateTimeInterface $DateDeb): static
    {
        $this->DateDeb = $DateDeb;

        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->DateFin;
    }

    public function setDateFin(?\DateTimeInterface $DateFin): static
    {
        $this->DateFin = $DateFin;

        return $this;
    }

    public function getDateReel(): ?\DateTimeInterface
    {
        return $this->dateReel;
    }

    public function setDateReel(?\DateTimeInterface $dateReel): static
    {
        $this->dateReel = $dateReel;

        return $this;
    }

    public function getCarrelage(): ?Carrelage
    {
        return $this->Carrelage;
    }

    public function setCarrelage(?Carrelage $Carrelage): static
    {
        $this->Carrelage = $Carrelage;

        return $this;
    }

    public function getEntretientPAC(): ?EntretientPAC
    {
        return $this->EntretientPAC;
    }

    public function setEntretientPAC(?EntretientPAC $EntretientPAC): static
    {
        $this->EntretientPAC = $EntretientPAC;

        return $this;
    }

    public function getChappe(): ?Chappe
    {
        return $this->Chappe;
    }

    public function setChappe(?Chappe $Chappe): static
    {
        $this->Chappe = $Chappe;

        return $this;
    }

    public function getCharpente(): ?Charpente
    {
        return $this->Charpente;
    }

    public function setCharpente(?Charpente $Charpente): static
    {
        $this->Charpente = $Charpente;

        return $this;
    }

    public function getEnduit(): ?Enduit
    {
        return $this->Enduit;
    }

    public function setEnduit(?Enduit $Enduit): static
    {
        $this->Enduit = $Enduit;

        return $this;
    }

    public function getEscalier(): ?Escalier
    {
        return $this->Escalier;
    }

    public function setEscalier(?Escalier $Escalier): static
    {
        $this->Escalier = $Escalier;

        return $this;
    }

    public function getEtancheite(): ?Etancheite
    {
        return $this->Etancheite;
    }

    public function setEtancheite(?Etancheite $Etancheite): static
    {
        $this->Etancheite = $Etancheite;

        return $this;
    }

    public function getFinition(): ?Finition
    {
        return $this->Finition;
    }

    public function setFinition(?Finition $Finition): static
    {
        $this->Finition = $Finition;

        return $this;
    }

    public function getLaineSouffle(): ?LaineSouffle
    {
        return $this->LaineSouffle;
    }

    public function setLaineSouffle(?LaineSouffle $LaineSouffle): static
    {
        $this->LaineSouffle = $LaineSouffle;

        return $this;
    }

    public function getMaconnerie(): ?Maconnerie
    {
        return $this->Maconnerie;
    }

    public function setMaconnerie(?Maconnerie $Maconnerie): static
    {
        $this->Maconnerie = $Maconnerie;

        return $this;
    }

    public function getMenuiserie(): ?Menuiserie
    {
        return $this->Menuiserie;
    }

    public function setMenuiserie(?Menuiserie $Menuiserie): static
    {
        $this->Menuiserie = $Menuiserie;

        return $this;
    }

    public function getPeinture(): ?Peinture
    {
        return $this->Peinture;
    }

    public function setPeinture(?Peinture $Peinture): static
    {
        $this->Peinture = $Peinture;

        return $this;
    }

    public function getPermeabilite(): ?Permeabilite
    {
        return $this->Permeabilite;
    }

    public function setPermeabilite(?Permeabilite $Permeabilite): static
    {
        $this->Permeabilite = $Permeabilite;

        return $this;
    }

    public function getPlaco(): ?Placo
    {
        return $this->Placo;
    }

    public function setPlaco(?Placo $Placo): static
    {
        $this->Placo = $Placo;

        return $this;
    }

    public function getPlomberieElec(): ?PlomberieElec
    {
        return $this->PlomberieElec;
    }

    public function setPlomberieElec(?PlomberieElec $PlomberieElec): static
    {
        $this->PlomberieElec = $PlomberieElec;

        return $this;
    }

    public function getTerrassement(): ?Terrassement
    {
        return $this->Terrassement;
    }

    public function setTerrassement(?Terrassement $Terrassement): static
    {
        $this->Terrassement = $Terrassement;

        return $this;
    }

    public function getZingurie(): ?Zinguerie
    {
        return $this->Zingurie;
    }

    public function setZingurie(?Zinguerie $Zingurie): static
    {
        $this->Zingurie = $Zingurie;

        return $this;
    }

    public function getZinguerie(): ?Zinguerie
    {
        return $this->Zingurie;
    }

    public function setZinguerie(?Zinguerie $Zingurie): static
    {
        $this->Zingurie = $Zingurie;

        return $this;
    }

    public function getCharpentier()
    {
        return isset($this->Charpente) ? $this->Charpente->getCharpentier() : null;
    }
    
    public function getEtancheur()
    {
        return isset($this->Etancheite) ? $this->Etancheite->getEtancheur() : null;
    }
    
    public function getArtisan()
    {
        return isset($this->Zingurie) ? $this->Zingurie->getArtisan() : null;
    }
    
    public function getMenuisier()
    {
        return isset($this->Menuiserie) ? $this->Menuiserie->getMenuisier() : null;
    }
    
    public function getPlaquiste()
    {
        return isset($this->Placo) ? $this->Placo->getPlaquiste() : null;
    }
    
    public function getEnduiseur()
    {
        return isset($this->Enduit) ? $this->Enduit->getEnduiseur() : null;
    }
    
    public function getCarreleur()
    {
        return isset($this->Carrelage) ? $this->Carrelage->getCarreleur() : null;
    }
    
    public function getFabricantEscalier()
    {
        return isset($this->Escalier) ? $this->Escalier->getFabricantEscalier() : null;
    }
    
    public function getPeintre()
    {
        return isset($this->Peinture) ? $this->Peinture->getPeintre() : null;
    }

    public function getPoseCarrelage()
    {
        return $this->Carrelage?->getPoseCarrelage();
    }

    public function getEtatPoseCarrelage()
    {
        return $this->Carrelage?->getEtatPoseCarrelage();
    }

    public function getPosePlinthe()
    {
        return $this->Carrelage?->getPosePlinthe();
    }

    public function getEtatPosePlinthe()
    {
        return $this->Carrelage?->getEtatPosePlinthe();
    }

    // Getters pour Chappe
    public function getChappeIntervention()
    {
        return $this->Chappe?->getIntervention();
    }

    public function getEtatChappeIntervention()
    {
        return $this->Chappe?->getEtatIntervention();
    }

    public function getCharpentes()
    {
        return $this->Charpente?->getCharpente();
    }

    public function getEtatCharpente()
    {
        return $this->Charpente?->getEtatCharpente();
    }

    public function getCouverture()
    {
        return $this->Charpente?->getCouverture();
    }

    public function getEtatCouverture()
    {
        return $this->Charpente?->getEtatCouverture();
    }

    // Getters pour Enduit
    public function getRavalement()
    {
        return $this->Enduit?->getIntervention();
    }

    public function getEtatRavalement()
    {
        return $this->Enduit?->getEtatIntervention();
    }

    // Getters pour Escalier
    public function getPoseEscalier()
    {
        return $this->Escalier?->getPoseEscalier();
    }

    public function getEtatPoseEscalier()
    {
        return $this->Escalier?->getEtatPoseEscalier();
    }

    // Getters pour Etanchéité
    public function getEtancheiteIntervention()
    {
        return $this->Etancheite?->getIntervention();
    }

    public function getEtatEtancheiteIntervention()
    {
        return $this->Etancheite?->getEtatIntervention();
    }

    // Getters pour Finition
    public function getFinitions()
    {
        return $this->Finition?->getFinition();
    }

    public function getEtatFinition()
    {
        return $this->Finition?->getEtatFinition();
    }

    // Getters pour Laine soufflée
    public function getLaineSouffleIntervention()
    {
        return $this->LaineSouffle?->getIntervention();
    }

    public function getEtatLaineSouffleIntervention()
    {
        return $this->LaineSouffle?->getEtatIntervention();
    }

    // Getters pour Maçonnerie
    public function getFondation()
    {
        return $this->Maconnerie?->getFondation();
    }

    public function getEtatFondation()
    {
        return $this->Maconnerie?->getEtatFondation();
    }

    public function getDalle()
    {
        return $this->Maconnerie?->getDalle();
    }

    public function getEtatDalle()
    {
        return $this->Maconnerie?->getEtatDalle();
    }

    public function getElevation()
    {
        return $this->Maconnerie?->getElevation();
    }

    public function getEtatElevation()
    {
        return $this->Maconnerie?->getEtatElevation();
    }

    public function getPrepaDalle()
    {
        return $this->PlomberieElec?->getPrepaDalle();
    }

    public function getEtatPrepaDalle()
    {
        return $this->PlomberieElec?->getEtatPrepaDalle();
    }

    public function getFinitionElec()
    {
        return $this->PlomberieElec?->getFinitionElec();
    }

    public function getEtatFinitionElec()
    {
        return $this->PlomberieElec?->getEtatFinitionElec();
    }

    public function getGrosOeuvre()
    {
        return $this->PlomberieElec?->getGrosOeuvre();
    }

    public function getEtatGrosOeuvre()
    {
        return $this->PlomberieElec?->getEtatGrosOeuvre();
    }

    public function getPosePACChaudiere()
    {
        return $this->PlomberieElec?->getPosePACChaudiere();
    }

    public function getEtatPosePACChaudiere()
    {
        return $this->PlomberieElec?->getEtatPosePACChaudiere();
    }

    public function getFinitionPlomberieSanitaire()
    {
        return $this->PlomberieElec?->getFinitionPlomberieSanitaire();
    }

    public function getEtatFinitionPlomberieSanitaire()
    {
        return $this->PlomberieElec?->getEtatFinitionPlomberieSanitaire();
    }

    // Getters pour Terrassement
    public function getBranchement()
    {
        return $this->Terrassement?->getBranchement();
    }

    public function getEtatBranchement()
    {
        return $this->Terrassement?->getEtatBranchement();
    }

    // Getters pour Maçonnerie
    public function getRampannage()
    {
        return $this->Maconnerie?->getRampannage();
    }

    public function getEtatRampannage()
    {
        return $this->Maconnerie?->getEtatRampannage();
    }

    public function getReleveSeuil()
    {
        return $this->Maconnerie?->getReleveSeuil();
    }

    public function getEtatReleveSeuil()
    {
        return $this->Maconnerie?->getEtatReleveSeuil();
    }

    public function getSeuil()
    {
        return $this->Maconnerie?->getSeuil();
    }

    public function getEtatSeuil()
    {
        return $this->Maconnerie?->getEtatSeuil();
    }

    public function getCouvertine()
    {
        return $this->Zingurie?->getCouvertine();
    }

    public function getEtatCouvertine()
    {
        return $this->Zingurie?->getEtatCouvertine();
    }

    public function getGoutiere()
    {
        return $this->Zingurie?->getGoutiere();
    }

    public function getEtatGoutiere()
    {
        return $this->Zingurie?->getEtatGoutiere();
    }

    public function getPoseMenuiserie(): ?string
    {
        return $this->Menuiserie?->getPoseMenuiserie();
    }

    public function getEtatPoseMenuiserie(): ?string
    {
        return $this->Menuiserie?->getEtatPoseMenuiserie();
    }

    public function getPrepaPlaco(): ?string
    {
        return $this->Placo?->getPrepaPlaco();
    }

    public function getEtatPrepaPlaco(): ?string
    {
        return $this->Placo?->getEtatPrepaPlaco();
    }

    public function getFinPlaco(): ?string
    {
        return $this->Placo?->getFinPlaco();
    }

    public function getEtatFinPlaco(): ?string
    {
        return $this->Placo?->getEtatFinPlaco();
    }

    public function getPrepaFileriePlomberie(): ?string
    {
        return $this->PlomberieElec?->getPrepaFileriePlomberie();
    }

    public function getEtatPrepaFileriePlomberie(): ?string
    {
        return $this->PlomberieElec?->getEtatPrepaFileriePlomberie();
    }

    public function getDescente(): ?string
    {
        return $this->Zingurie?->getDescente();
    }

    public function getEtatDescente(): ?string
    {
        return $this->Zingurie?->getEtatDescente();
    }

    public function getCloison(): ?string
    {
        return $this->Placo?->getCloison();
    }

    public function getEtatCloison(): ?string
    {
        return $this->Placo?->getEtatCloison();
    }

    public function getPermeabiliteIntervention()
    {
        return $this->Permeabilite?->getIntervention();
    }

    public function getEtatPermeabiliteIntervention()
    {
        return $this->Permeabilite?->getEtatIntervention();
    }

    public function getEtatPeinture()
    {
        return $this->Peinture?->getEtatPeinture();
    }

    public function setEtatPeinture($etatPeinture)
    {
        $this->Peinture?->setEtatPeinture($etatPeinture);
    }

    public function getPeintureIntervention()
    {
        return $this->Peinture?->getIntervention();
    }

    public function setPeintureIntervention($Intervention)
    {
        $this->Peinture?->setIntervention($Intervention);
    }

    public function getMES()
    {
        return $this->EntretientPAC?->getMES();
    }

    public function getEtatMES()
    {
        return $this->EntretientPAC?->getEtatMES();
    }

    public function setCharpentier($charpentier)
    {
        if (isset($this->Charpente)) {
            $this->Charpente->setCharpentier($charpentier);
        }
    }

    public function setEtancheur($etancheur)
    {
        if (isset($this->Etancheite)) {
            $this->Etancheite->setEtancheur($etancheur);
        }
    }

    public function setArtisan($artisan)
    {
        if (isset($this->Zingurie)) {
            $this->Zingurie->setArtisan($artisan);
        }
    }

    
    public function setTechnicienPAC($entretientPAC)
    {
        if (isset($this->EntretientPAC)) {
            $this->EntretientPAC->setArtisan($entretientPAC);
        }
    }


    public function setMenuisier($menuisier)
    {
        if (isset($this->Menuiserie)) {
            $this->Menuiserie->setMenuisier($menuisier);
        }
    }

    public function setPlaquiste($plaquiste)
    {
        if (isset($this->Placo)) {
            $this->Placo->setPlaquiste($plaquiste);
        }
    }

    public function setEnduiseur($enduiseur)
    {
        if (isset($this->Enduit)) {
            $this->Enduit->setEnduiseur($enduiseur);
        }
    }

    public function setCarreleur($carreleur)
    {
        if (isset($this->Carrelage)) {
            $this->Carrelage->setCarreleur($carreleur);
        }
    }

    public function setFabricantEscalier($fabricantEscalier)
    {
        if (isset($this->Escalier)) {
            $this->Escalier->setFabricantEscalier($fabricantEscalier);
        }
    }

    public function setPeintre($peintre)
    {
        if (isset($this->Peinture)) {
            $this->Peinture->setPeintre($peintre);
        }
    }

    public function setPoseCarrelage($poseCarrelage)
    {
        $this->Carrelage?->setPoseCarrelage($poseCarrelage);
    }

    public function setEtatPoseCarrelage($etatPoseCarrelage)
    {
        $this->Carrelage?->setEtatPoseCarrelage($etatPoseCarrelage);
    }

    public function setPosePlinthe($posePlinthe)
    {
        $this->Carrelage?->setPosePlinthe($posePlinthe);
    }

    public function setEtatPosePlinthe($etatPosePlinthe)
    {
        $this->Carrelage?->setEtatPosePlinthe($etatPosePlinthe);
    }

    public function setChappeIntervention($chappeIntervention)
    {
        $this->Chappe?->setIntervention($chappeIntervention);
    }

    public function setEtatChappeIntervention($etatChappeIntervention)
    {
        $this->Chappe?->setEtatIntervention($etatChappeIntervention);
    }

    public function setCharpentes($charpentes)
    {
        $this->Charpente?->setCharpente($charpentes);
    }

    public function setEtatCharpente($etatCharpente)
    {
        $this->Charpente?->setEtatCharpente($etatCharpente);
    }

    public function setCouverture($couverture)
    {
        $this->Charpente?->setCouverture($couverture);
    }

    public function setEtatCouverture($etatCouverture)
    {
        $this->Charpente?->setEtatCouverture($etatCouverture);
    }

    public function setRavalement($ravalement)
    {
        $this->Enduit?->setIntervention($ravalement);
    }

    public function setEtatRavalement($etatRavalement)
    {
        $this->Enduit?->setEtatIntervention($etatRavalement);
    }

    public function setPoseEscalier($poseEscalier)
    {
        $this->Escalier?->setPoseEscalier($poseEscalier);
    }

    public function setEtatPoseEscalier($etatPoseEscalier)
    {
        $this->Escalier?->setEtatPoseEscalier($etatPoseEscalier);
    }

    public function setEtancheiteIntervention($etancheiteIntervention)
    {
        $this->Etancheite?->setIntervention($etancheiteIntervention);
    }

    public function setEtatEtancheiteIntervention($etatEtancheiteIntervention)
    {
        $this->Etancheite?->setEtatIntervention($etatEtancheiteIntervention);
    }

    public function setFinitions($finitions)
    {
        $this->Finition?->setFinition($finitions);
    }

    public function setEtatFinition($etatFinition)
    {
        $this->Finition?->setEtatFinition($etatFinition);
    }

    public function setLaineSouffleIntervention($laineSouffleIntervention)
    {
        $this->LaineSouffle?->setIntervention($laineSouffleIntervention);
    }

    public function setEtatLaineSouffleIntervention($etatLaineSouffleIntervention)
    {
        $this->LaineSouffle?->setEtatIntervention($etatLaineSouffleIntervention);
    }

    public function setFondation($fondation)
    {
        $this->Maconnerie?->setFondation($fondation);
    }

    public function setEtatFondation($etatFondation)
    {
        $this->Maconnerie?->setEtatFondation($etatFondation);
    }

    public function setDalle($dalle)
    {
        $this->Maconnerie?->setDalle($dalle);
    }

    public function setEtatDalle($etatDalle)
    {
        $this->Maconnerie?->setEtatDalle($etatDalle);
    }

    public function setElevation($elevation)
    {
        $this->Maconnerie?->setElevation($elevation);
    }

    public function setEtatElevation($etatElevation)
    {
        $this->Maconnerie?->setEtatElevation($etatElevation);
    }

    public function setPrepaDalle($prepaDalle)
    {
        $this->PlomberieElec?->setPrepaDalle($prepaDalle);
    }

    public function setEtatPrepaDalle($etatPrepaDalle)
    {
        $this->PlomberieElec?->setEtatPrepaDalle($etatPrepaDalle);
    }

    public function setFinitionElec($finitionElec)
    {
        $this->PlomberieElec?->setFinitionElec($finitionElec);
    }

    public function setEtatFinitionElec($etatFinitionElec)
    {
        $this->PlomberieElec?->setEtatFinitionElec($etatFinitionElec);
    }

    public function setGrosOeuvre($grosOeuvre)
    {
        $this->PlomberieElec?->setGrosOeuvre($grosOeuvre);
    }

    public function setEtatGrosOeuvre($etatGrosOeuvre)
    {
        $this->PlomberieElec?->setEtatGrosOeuvre($etatGrosOeuvre);
    }

    public function setPosePACChaudiere($posePACChaudiere)
    {
        $this->PlomberieElec?->setPosePACChaudiere($posePACChaudiere);
    }

    public function setEtatPosePACChaudiere($etatPosePACChaudiere)
    {
        $this->PlomberieElec?->setEtatPosePACChaudiere($etatPosePACChaudiere);
    }

    public function setFinitionPlomberieSanitaire($finitionPlomberieSanitaire)
    {
        $this->PlomberieElec?->setFinitionPlomberieSanitaire($finitionPlomberieSanitaire);
    }

    public function setEtatFinitionPlomberieSanitaire($etatFinitionPlomberieSanitaire)
    {
        $this->PlomberieElec?->setEtatFinitionPlomberieSanitaire($etatFinitionPlomberieSanitaire);
    }

    public function setBranchement($branchement)
    {
        $this->Terrassement?->setBranchement($branchement);
    }

    public function setEtatBranchement($etatBranchement)
    {
        $this->Terrassement?->setEtatBranchement($etatBranchement);
    }

    public function setRampannage($rampannage)
    {
        $this->Maconnerie?->setRampannage($rampannage);
    }

    public function setEtatRampannage($etatRampannage)
    {
        $this->Maconnerie?->setEtatRampannage($etatRampannage);
    }

    public function setReleveSeuil($releveSeuil)
    {
        $this->Maconnerie?->setReleveSeuil($releveSeuil);
    }

    public function setEtatReleveSeuil($etatReleveSeuil)
    {
        $this->Maconnerie?->setEtatReleveSeuil($etatReleveSeuil);
    }

    public function setSeuil($seuil)
    {
        $this->Maconnerie?->setSeuil($seuil);
    }

    public function setEtatSeuil($etatSeuil)
    {
        $this->Maconnerie?->setEtatSeuil($etatSeuil);
    }

    public function setCouvertine($Couvertine)
    {
        $this->Zingurie?->setCouvertine($Couvertine);
    }

    public function setEtatCouvertine($etatCouvertine)
    {
        $this->Zingurie?->setEtatCouvertine($etatCouvertine);
    }

    public function setGoutiere($Goutiere)
    {
        $this->Zingurie?->setGoutiere($Goutiere);
    }

    public function setEtatGoutiere($etatGoutiere)
    {
        $this->Zingurie?->setEtatGoutiere($etatGoutiere);
    }

    public function setPoseMenuiserie($PoseMenuiserie)
    {
        $this->Menuiserie?->setPoseMenuiserie($PoseMenuiserie);
    }

    public function setEtatPoseMenuiserie($etatPoseMenuiserie)
    {
        $this->Menuiserie?->setEtatPoseMenuiserie($etatPoseMenuiserie);
    }

    public function setPrepaPlaco($prepaPlaco)
    {
        $this->Placo?->setPrepaPlaco($prepaPlaco);
    }

    public function setEtatPrepaPlaco($etatPrepaPlaco)
    {
        $this->Placo?->setEtatPrepaPlaco($etatPrepaPlaco);
    }

    public function setFinPlaco($finPlaco)
    {
        $this->Placo?->setFinPlaco($finPlaco);
    }

    public function setEtatFinPlaco($etatFinPlaco)
    {
        $this->Placo?->setEtatFinPlaco($etatFinPlaco);
    }

    public function setPrepaFileriePlomberie($prepaFileriePlomberie)
    {
        $this->PlomberieElec?->setPrepaFileriePlomberie($prepaFileriePlomberie);
    }

    public function setEtatPrepaFileriePlomberie($etatPrepaFileriePlomberie)
    {
        $this->PlomberieElec?->setEtatPrepaFileriePlomberie($etatPrepaFileriePlomberie);
    }

    public function setDescente($Descente)
    {
        $this->Zingurie?->setDescente($Descente);
    }

    public function setEtatDescente($etatDescente)
    {
        $this->Zingurie?->setEtatDescente($etatDescente);
    }

    public function setCloison($cloison)
    {
        $this->Placo?->setCloison($cloison);
    }

    public function setEtatCloison($etatCloison)
    {
        $this->Placo?->setEtatCloison($etatCloison);
    }

    public function setPermeabiliteIntervention($permeabiliteIntervention)
    {
        $this->Permeabilite?->setIntervention($permeabiliteIntervention);
    }

    public function setEtatPermeabiliteIntervention($etatPermeabiliteIntervention)
    {
        $this->Permeabilite?->setEtatIntervention($etatPermeabiliteIntervention);
    }
    
    public function setMES($MES)
    {
        $this->EntretientPAC?->setMES($MES);
    }

    public function setEtatMES($etatMES)
    {
        $this->EntretientPAC?->setEtatMES($etatMES);
    }


    public function getChantierArray()
    {
        return [
            'id' => $this->id,
            'Client' => $this->Client,
            'Commune' => $this->Commune,
            'CDT' => $this->CDT,
            'DateDeb' => $this->DateDeb,
            'DateFin' => $this->DateFin,
            'dateReel' => $this->dateReel,
            'mouvements' => $this->mouvements,
        ];
    }

    public function getAllArtisans(): array
    {
        return [
            'Terrassement' => $this->Terrassement?->getArtisan()?->getId(),
            'Maconnerie' => $this->Maconnerie?->getArtisan()?->getId(),
            'PlomberieElec' => $this->PlomberieElec?->getArtisan()?->getId(),
            'Charpente' => $this->Charpente?->getArtisan()?->getId(),
            'Etancheite' => $this->Etancheite?->getArtisan()?->getId(),
            'Zinguerie' => $this->Zingurie?->getArtisan()?->getId(),
            'Menuiserie' => $this->Menuiserie?->getArtisan()?->getId(),
            'Placo' => $this->Placo?->getArtisan()?->getId(),
            'Chappe' => $this->Chappe?->getArtisan()?->getId(),
            'Enduit' => $this->Enduit?->getArtisan()?->getId(),
            'LaineSouffle' => $this->LaineSouffle?->getArtisan()?->getId(),
            'Carrelage' => $this->Carrelage?->getArtisan()?->getId(),
            'Escalier' => $this->Escalier?->getArtisan()?->getId(),
            'Finition' => $this->Finition?->getArtisan()?->getId(),
            'Peinture' => $this->Peinture?->getArtisan()?->getId(),
            'Permeabilite' => $this->Permeabilite?->getArtisan()?->getId(),
            'EntretientPAC' => $this->EntretientPAC?->getArtisan()?->getId(),
        ];
    }

    public function setArtisanTable($metier, $artisan)
    {
        switch ($metier) {
            case 'Terrassement':
                $this->Terrassement?->setArtisan($artisan);
                break;
            case 'Maconnerie':
                $this->Maconnerie?->setArtisan($artisan);
                break;
            case 'PlomberieElec':
                $this->PlomberieElec?->setArtisan($artisan);
                break;
            case 'Charpente':
                $this->Charpente?->setArtisan($artisan);
                break;
            case 'Etancheite':
                $this->Etancheite?->setArtisan($artisan);
                break;
            case 'Zinguerie':
                $this->Zingurie?->setArtisan($artisan);
                break;
            case 'Menuiserie':
                $this->Menuiserie?->setArtisan($artisan);
                break;
            case 'Placo':
                $this->Placo?->setArtisan($artisan);
                break;
            case 'Chappe':
                $this->Chappe?->setArtisan($artisan);
                break;
            case 'Enduit':
                $this->Enduit?->setArtisan($artisan);
                break;
            case 'LaineSouffle':
                $this->LaineSouffle?->setArtisan($artisan);
                break;
            case 'Carrelage':
                $this->Carrelage?->setArtisan($artisan);
                break;
            case 'Escalier':
                $this->Escalier?->setArtisan($artisan);
                break;
            case 'Finition':
                $this->Finition?->setArtisan($artisan);
                break;
            case 'Peinture':
                $this->Peinture?->setArtisan($artisan);
                break;
            case 'Permeabilite':
                $this->Permeabilite?->setArtisan($artisan);
                break;
            case 'EntretientPAC':
                $this->EntretientPAC?->setArtisan($artisan);
                break;
        }
    }

    public function isArchive(): ?bool
    {
        return $this->archive;
    }

    public function setArchive(?bool $archive): static
    {
        $this->archive = $archive;

        return $this;
    }

    public function getMouvements(): ?string
    {
        return $this->mouvements;
    }

    public function setMouvements(?string $mouvements): static
    {
        $this->mouvements = $this->mouvements ? $this->mouvements . "\n" . $mouvements : $mouvements;

        return $this;
    }
}
