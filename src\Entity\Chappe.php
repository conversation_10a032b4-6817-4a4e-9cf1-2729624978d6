<?php

namespace App\Entity;

use App\Repository\ChappeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ChappeRepository::class)]
class Chappe
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'chappes')]
    private ?Artisan $Artisan = null;

    #[ORM\Column(nullable: true)]
    private ?int $Intervention = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatIntervention = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Chappe', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Artisan;
    }

    public function setArtisan(?Artisan $Artisan): static
    {
        $this->Artisan = $Artisan;

        return $this;
    }

    public function getIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function getChappeIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function setIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function setChappeIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function getEtatIntervention(): ?string
    {
        return $this->EtatIntervention;
    }

    public function setEtatIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function setEtatChappeIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }
    
    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setChappe(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getChappe() !== $this) {
            $chantier->setChappe($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Artisan?->getId(),
            ],
            'Chappe' => [
                'nomAttribut' => 'Intervention',
                'week' => $this->Intervention,
                'etat' => $this->EtatIntervention
            ]
        ];
    }


}
