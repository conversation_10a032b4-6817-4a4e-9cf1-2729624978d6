<?php

namespace App\Entity;

use App\Repository\CharpenteRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CharpenteRepository::class)]
class Charpente
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'charpentes')]
    private ?Artisan $Charpentier = null;

    #[ORM\Column(nullable: true)]
    private ?int $Charpente = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatCharpente = null;

    #[ORM\Column(nullable: true)]
    private ?int $Couverture = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatCouverture = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Charpente', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCharpentier(): ?Artisan
    {
        return $this->Charpentier;
    }

    public function setCharpentier(?Artisan $Charpentier): static
    {
        $this->Charpentier = $Charpentier;

        return $this;
    }

    public function getCharpente(): ?int
    {
        return $this->Charpente;
    }

    public function setCharpente(?int $Charpente): static
    {
        $this->Charpente = $Charpente;

        return $this;
    }

    public function getCharpentes(): ?int
    {
        return $this->Charpente;
    }

    public function setCharpentes(?int $Charpente): static
    {
        $this->Charpente = $Charpente;

        return $this;
    }

    public function getEtatCharpente(): ?string
    {
        return $this->EtatCharpente;
    }

    public function setEtatCharpente(?string $EtatCharpente): static
    {
        $this->EtatCharpente = $EtatCharpente;

        return $this;
    }

    public function getCouverture(): ?int
    {
        return $this->Couverture;
    }

    public function setCouverture(?int $Couverture): static
    {
        $this->Couverture = $Couverture;

        return $this;
    }

    public function getEtatCouverture(): ?string
    {
        return $this->EtatCouverture;
    }

    public function setEtatCouverture(?string $EtatCouverture): static
    {
        $this->EtatCouverture = $EtatCouverture;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setCharpente(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getCharpente() !== $this) {
            $chantier->setCharpente($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Charpentier;
    }

    public function setArtisan(?Artisan $Charpentier): static
    {
        $this->Charpentier = $Charpentier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Charpentier?->getId(),
            ],
            'Charpente' => [
                'nomAttribut' => 'Charpente',
                'week' => $this->Charpente,
                'etat' => $this->EtatCharpente
            ],
            'Couverture' => [
                'nomAttribut' => 'Couverture',
                'week' => $this->Couverture,
                'etat' => $this->EtatCouverture
            ],
            'Observation' => $this->Observation,
        ];
    }

}
