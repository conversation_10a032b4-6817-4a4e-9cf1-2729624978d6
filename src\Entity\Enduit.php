<?php

namespace App\Entity;

use App\Repository\EnduitRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EnduitRepository::class)]
class Enduit
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'enduits')]
    private ?Artisan $Enduiseur = null;

    #[ORM\Column(nullable: true)]
    private ?int $Intervention = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatIntervention = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Enduit', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEnduiseur(): ?Artisan
    {
        return $this->Enduiseur;
    }

    public function setEnduiseur(?Artisan $Enduiseur): static
    {
        $this->Enduiseur = $Enduiseur;

        return $this;
    }

    public function getIntervention(): ?int
    {
        return $this->Intervention;
    }


    public function setIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function setRavalement(?int $Ravalement): static
    {
        $this->Intervention = $Ravalement;

        return $this;
    }

    public function getRavalement(): ?int
    {
        return $this->Intervention;
    }

    public function setEtatRavalement(?string $EtatRavalement): static
    {
        $this->EtatIntervention = $EtatRavalement;

        return $this;
    }

    public function getEtatIntervention(): ?string
    {
        return $this->EtatIntervention;
    }

    public function setEtatIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setEnduit(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getEnduit() !== $this) {
            $chantier->setEnduit($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Enduiseur;
    }

    public function setArtisan(?Artisan $Enduiseur): static
    {
        $this->Enduiseur = $Enduiseur;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Enduiseur?->getId(),
            ],
            'Enduit' => [
                'nomAttribut' => 'Intervention',
                'week' => $this->Intervention,
                'etat' => $this->EtatIntervention
            ],
            'Observation' => $this->Observation,
        ];
    }

}
