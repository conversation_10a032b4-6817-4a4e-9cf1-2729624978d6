<?php

namespace App\Entity;

use App\Repository\EntretientPACRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EntretientPACRepository::class)]
class EntretientPAC
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'entretientPACs')]
    private ?Artisan $Artisan = null;

    #[ORM\Column(nullable: true)]
    private ?int $MES = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatMES = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'EntretientPAC', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Artisan;
    }

    public function setArtisan(?Artisan $Artisan): static
    {
        $this->Artisan = $Artisan;

        return $this;
    }

    public function getMES(): ?int
    {
        return $this->MES;
    }

    public function setMES(?int $MES): static
    {
        $this->MES = $MES;

        return $this;
    }

    public function getEtatMES(): ?string
    {
        return $this->EtatMES;
    }

    public function setEtatMES(?string $EtatMES): static
    {
        $this->EtatMES = $EtatMES;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setEntretientPAC(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getEntretientPAC() !== $this) {
            $chantier->setEntretientPAC($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Artisan?->getId(),
            ],
            'MES' => [
                'nomAttribut' => 'MES',
                'week' => $this->MES,
                'etat' => $this->EtatMES
            ],
            'Observation' => $this->Observation,
        ];
    }

}






