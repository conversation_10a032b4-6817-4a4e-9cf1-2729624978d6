<?php

namespace App\Entity;

use App\Repository\EscalierRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EscalierRepository::class)]
class Escalier
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'escaliers')]
    private ?Artisan $Fabricant = null;

    #[ORM\Column(nullable: true)]
    private ?int $PriseCote = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPriseCote = null;

    #[ORM\Column(nullable: true)]
    private ?int $PoseEscalier = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPoseEscalier = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Escalier', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFabricant(): ?Artisan
    {
        return $this->Fabricant;
    }

    public function setFabricant(?Artisan $Fabricant): static
    {
        $this->Fabricant = $Fabricant;

        return $this;
    }

    public function getPriseCote(): ?int
    {
        return $this->PriseCote;
    }

    public function setPriseCote(?int $PriseCote): static
    {
        $this->PriseCote = $PriseCote;

        return $this;
    }

    public function getEtatPriseCote(): ?string
    {
        return $this->EtatPriseCote;
    }

    public function setEtatPriseCote(?string $EtatPriseCote): static
    {
        $this->EtatPriseCote = $EtatPriseCote;

        return $this;
    }

    public function getPoseEscalier(): ?int
    {
        return $this->PoseEscalier;
    }

    public function setPoseEscalier(?int $PoseEscalier): static
    {
        $this->PoseEscalier = $PoseEscalier;

        return $this;
    }

    public function getEtatPoseEscalier(): ?string
    {
        return $this->EtatPoseEscalier;
    }

    public function setEtatPoseEscalier(?string $EtatPoseEscalier): static
    {
        $this->EtatPoseEscalier = $EtatPoseEscalier;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setEscalier(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getEscalier() !== $this) {
            $chantier->setEscalier($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Fabricant;
    }

    public function setArtisan(?Artisan $Fabricant): static
    {
        $this->Fabricant = $Fabricant;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Fabricant?->getId(),
            ],
            'Prise Cote' => [
                'nomAttribut' => 'PriseCote',
                'week' => $this->PriseCote,
                'etat' => $this->EtatPriseCote
            ],
            'Pose Escalier' => [
                'nomAttribut' => 'PoseEscalier',
                'week' => $this->PoseEscalier,
                'etat' => $this->EtatPoseEscalier
            ],
            'Observation' => $this->Observation,
        ];
    }

}
