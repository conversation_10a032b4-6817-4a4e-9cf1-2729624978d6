<?php

namespace App\Entity;

use App\Repository\EtancheiteRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EtancheiteRepository::class)]
class Etancheite
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'etancheites')]
    private ?Artisan $Etancheur = null;

    #[ORM\Column(nullable: true)]
    private ?int $Intervention = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatIntervention = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Etancheite', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEtancheur(): ?Artisan
    {
        return $this->Etancheur;
    }

    public function setEtancheur(?Artisan $Etancheur): static
    {
        $this->Etancheur = $Etancheur;

        return $this;
    }

    public function getIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function getEtancheiteIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function setIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function setEtancheiteIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function getEtatIntervention(): ?string
    {
        return $this->EtatIntervention;
    }

    public function setEtatIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function setEtatEtancheiteIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setEtancheite(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getEtancheite() !== $this) {
            $chantier->setEtancheite($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Etancheur;
    }

    public function setArtisan(?Artisan $artisan): static
    {
        $this->Etancheur = $artisan;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Etancheur?->getId(),
            ],
            'Etancheite' => [
                'nomAttribut' => 'Intervention',
                'week' => $this->Intervention,
                'etat' => $this->EtatIntervention
            ],
            'Observation' => $this->Observation
        ];
    }

}
