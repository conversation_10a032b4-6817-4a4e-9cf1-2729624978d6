<?php

namespace App\Entity;

use App\Repository\FinitionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FinitionRepository::class)]
class Finition
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'finitions')]
    private ?Artisan $Artisan = null;

    #[ORM\Column(nullable: true)]
    private ?int $Finition = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatFinition = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Finition', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Artisan;
    }

    public function setArtisan(?Artisan $Artisan): static
    {
        $this->Artisan = $Artisan;

        return $this;
    }

    public function getFinition(): ?int
    {
        return $this->Finition;
    }

    public function setFinition(?int $Finition): static
    {
        $this->Finition = $Finition;

        return $this;
    }

    public function getFinitions(): ?string
    {
        return $this->EtatFinition;
    }

    public function setFinitions(?int $Finition): static
    {
        $this->Finition = $Finition;

        return $this;
    }

    public function getEtatFinition(): ?string
    {
        return $this->EtatFinition;
    }

    public function setEtatFinition(?string $EtatFinition): static
    {
        $this->EtatFinition = $EtatFinition;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setFinition(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getFinition() !== $this) {
            $chantier->setFinition($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Artisan?->getId(),
            ],
            'Finition' => [
                'nomAttribut' => 'Finition',
                'week' => $this->Finition,
                'etat' => $this->EtatFinition
            ],
            'Observation' => $this->Observation,
        ];
    }

}
