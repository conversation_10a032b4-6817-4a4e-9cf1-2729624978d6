<?php

namespace App\Entity;

use App\Repository\MaconnerieRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MaconnerieRepository::class)]
class Maconnerie
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'maconneries')]
    private ?Artisan $Macon = null;

    #[ORM\Column(nullable: true)]
    private ?int $Fondation = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatFondation = null;

    #[ORM\Column(nullable: true)]
    private ?int $Dalle = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatDalle = null;

    #[ORM\Column(nullable: true)]
    private ?int $Elevation = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatElevation = null;

    #[ORM\Column(nullable: true)]
    private ?int $Rampannage = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatRampannage = null;

    #[ORM\Column(nullable: true)]
    private ?int $ReleveSeuil = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatReleveSeuil = null;

    #[ORM\Column(nullable: true)]
    private ?int $Seuil = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatSeuil = null;

    #[ORM\Column(nullable: true)]
    private ?int $Terrasse = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatTerrasse = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Maconnerie', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMacon(): ?Artisan
    {
        return $this->Macon;
    }

    public function setMacon(?Artisan $Macon): static
    {
        $this->Macon = $Macon;

        return $this;
    }

    public function getFondation(): ?int
    {
        return $this->Fondation;
    }

    public function setFondation(?int $Fondation): static
    {
        $this->Fondation = $Fondation;

        return $this;
    }

    public function getEtatFondation(): ?string
    {
        return $this->EtatFondation;
    }

    public function setEtatFondation(?string $EtatFondation): static
    {
        $this->EtatFondation = $EtatFondation;

        return $this;
    }

    public function getDalle(): ?int
    {
        return $this->Dalle;
    }

    public function setDalle(?int $Dalle): static
    {
        $this->Dalle = $Dalle;

        return $this;
    }

    public function getEtatDalle(): ?string
    {
        return $this->EtatDalle;
    }

    public function setEtatDalle(?string $EtatDalle): static
    {
        $this->EtatDalle = $EtatDalle;

        return $this;
    }

    public function getElevation(): ?int
    {
        return $this->Elevation;
    }

    public function setElevation(?int $Elevation): static
    {
        $this->Elevation = $Elevation;

        return $this;
    }

    public function getEtatElevation(): ?string
    {
        return $this->EtatElevation;
    }

    public function setEtatElevation(?string $EtatElevation): static
    {
        $this->EtatElevation = $EtatElevation;

        return $this;
    }

    public function getRampannage(): ?int
    {
        return $this->Rampannage;
    }

    public function setRampannage(?int $Rampannage): static
    {
        $this->Rampannage = $Rampannage;

        return $this;
    }

    public function getEtatRampannage(): ?string
    {
        return $this->EtatRampannage;
    }

    public function setEtatRampannage(?string $EtatRampannage): static
    {
        $this->EtatRampannage = $EtatRampannage;

        return $this;
    }

    public function getReleveSeuil(): ?int
    {
        return $this->ReleveSeuil;
    }

    public function setReleveSeuil(?int $ReleveSeuil): static
    {
        $this->ReleveSeuil = $ReleveSeuil;

        return $this;
    }

    public function getEtatReleveSeuil(): ?string
    {
        return $this->EtatReleveSeuil;
    }

    public function setEtatReleveSeuil(?string $EtatReleveSeuil): static
    {
        $this->EtatReleveSeuil = $EtatReleveSeuil;

        return $this;
    }

    public function getSeuil(): ?int
    {
        return $this->Seuil;
    }

    public function setSeuil(?int $Seuil): static
    {
        $this->Seuil = $Seuil;

        return $this;
    }

    public function getEtatSeuil(): ?string
    {
        return $this->EtatSeuil;
    }

    public function setEtatSeuil(?string $EtatSeuil): static
    {
        $this->EtatSeuil = $EtatSeuil;

        return $this;
    }

    public function getTerrasse(): ?int
    {
        return $this->Terrasse;
    }

    public function setTerrasse(?int $Terrasse): static
    {
        $this->Terrasse = $Terrasse;

        return $this;
    }

    public function getEtatTerrasse(): ?string
    {
        return $this->EtatTerrasse;
    }

    public function setEtatTerrasse(?string $EtatTerrasse): static
    {
        $this->EtatTerrasse = $EtatTerrasse;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setMaconnerie(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getMaconnerie() !== $this) {
            $chantier->setMaconnerie($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Macon;
    }

    public function setArtisan(?Artisan $Macon): static
    {
        $this->Macon = $Macon;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Macon?->getId(),
            ],
            'Fondation' => [
                'nomAttribut' => 'Fondation',
                'week' => $this->Fondation,
                'etat' => $this->EtatFondation,
            ],
            'Dalle' => [
                'nomAttribut' => 'Dalle',
                'week' => $this->Dalle,
                'etat' => $this->EtatDalle,
            ],
            'Elevation' => [
                'nomAttribut' => 'Elevation',
                'week' => $this->Elevation,
                'etat' => $this->EtatElevation,
            ],
            'Rampannage' => [
                'nomAttribut' => 'Rampannage',
                'week' => $this->Rampannage,
                'etat' => $this->EtatRampannage,
            ],
            'Releve Seuil' => [
                'nomAttribut' => 'ReleveSeuil',
                'week' => $this->ReleveSeuil,
                'etat' => $this->EtatReleveSeuil,
            ],
            'Seuil' => [
                'nomAttribut' => 'Seuil',
                'week' => $this->Seuil,
                'etat' => $this->EtatSeuil,
            ],
            'Terrasse' => [
                'nomAttribut' => 'Terrasse',
                'week' => $this->Terrasse,
                'etat' => $this->EtatTerrasse,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
