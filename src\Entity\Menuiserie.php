<?php

namespace App\Entity;

use App\Repository\MenuiserieRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MenuiserieRepository::class)]
class Menuiserie
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'menuiseries')]
    private ?Artisan $Menuisier = null;

    #[ORM\Column(nullable: true)]
    private ?int $PoseMenuiserie = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPoseMenuiserie = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Menuiserie', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMenuisier(): ?Artisan
    {
        return $this->Menuisier;
    }

    public function setMenuisier(?Artisan $Menuisier): static
    {
        $this->Menuisier = $Menuisier;

        return $this;
    }

    public function getPoseMenuiserie(): ?int
    {
        return $this->PoseMenuiserie;
    }

    public function setPoseMenuiserie(?int $PoseMenuiserie): static
    {
        $this->PoseMenuiserie = $PoseMenuiserie;

        return $this;
    }

    public function getEtatPoseMenuiserie(): ?string
    {
        return $this->EtatPoseMenuiserie;
    }

    public function setEtatPoseMenuiserie(?string $EtatPoseMenuiserie): static
    {
        $this->EtatPoseMenuiserie = $EtatPoseMenuiserie;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setMenuiserie(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getMenuiserie() !== $this) {
            $chantier->setMenuiserie($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Menuisier;
    }

    public function setArtisan(?Artisan $Menuisier): static
    {
        $this->Menuisier = $Menuisier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Menuisier?->getId(),
            ],
            'Pose Menuiserie' => [
                'nomAttribut' => 'PoseMenuiserie',
                'week' => $this->PoseMenuiserie,
                'etat' => $this->EtatPoseMenuiserie,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
