<?php

namespace App\Entity;

use App\Repository\PeintureRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PeintureRepository::class)]
class Peinture
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'peintures')]
    private ?Artisan $Peintre = null;

    #[ORM\Column(nullable: true)]
    private ?int $Intervention = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatIntervention = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Peinture', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPeintre(): ?Artisan
    {
        return $this->Peintre;
    }

    public function setPeintre(?Artisan $Peintre): static
    {
        $this->Peintre = $Peintre;

        return $this;
    }

    public function getIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function getPeintureIntervention(): ?int
    {
        return $this->Intervention;
    }

    public function setIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }

    public function setPeintureIntervention(?int $Intervention): static
    {
        $this->Intervention = $Intervention;

        return $this;
    }
    
    public function setEtatPeinture(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function getEtatIntervention(): ?string
    {
        return $this->EtatIntervention;
    }

    public function getEtatPeinture(): ?string
    {
        return $this->EtatIntervention;
    }

    public function setEtatIntervention(?string $EtatIntervention): static
    {
        $this->EtatIntervention = $EtatIntervention;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setPeinture(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getPeinture() !== $this) {
            $chantier->setPeinture($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Peintre;
    }

    public function setArtisan(?Artisan $Peintre): static
    {
        $this->Peintre = $Peintre;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Peintre?->getId(),  // Replace with actual property if you want the name instead of ID
            ],
            'Peinture' => [
                'nomAttribut' => 'Intervention',
                'week' => $this->Intervention,
                'etat' => $this->EtatIntervention,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
