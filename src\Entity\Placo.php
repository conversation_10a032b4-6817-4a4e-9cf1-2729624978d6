<?php

namespace App\Entity;

use App\Repository\PlacoRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlacoRepository::class)]
class Placo
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'placos')]
    private ?Artisan $Plaquiste = null;

    #[ORM\Column(nullable: true)]
    private ?int $PrepaPlaco = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPrepaPlaco = null;

    #[ORM\Column(nullable: true)]
    private ?int $FinPlaco = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatFinPlaco = null;

    #[ORM\Column(nullable: true)]
    private ?int $Cloison = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatCloison = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Placo', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlaquiste(): ?Artisan
    {
        return $this->Plaquiste;
    }

    public function setPlaquiste(?Artisan $Plaquiste): static
    {
        $this->Plaquiste = $Plaquiste;

        return $this;
    }

    public function getPrepaPlaco(): ?int
    {
        return $this->PrepaPlaco;
    }

    public function setPrepaPlaco(?int $PrepaPlaco): static
    {
        $this->PrepaPlaco = $PrepaPlaco;

        return $this;
    }

    public function getEtatPrepaPlaco(): ?string
    {
        return $this->EtatPrepaPlaco;
    }

    public function setEtatPrepaPlaco(?string $EtatPrepaPlaco): static
    {
        $this->EtatPrepaPlaco = $EtatPrepaPlaco;

        return $this;
    }

    public function getFinPlaco(): ?int
    {
        return $this->FinPlaco;
    }

    public function setFinPlaco(?int $FinPlaco): static
    {
        $this->FinPlaco = $FinPlaco;

        return $this;
    }

    public function getEtatFinPlaco(): ?string
    {
        return $this->EtatFinPlaco;
    }

    public function setEtatFinPlaco(?string $EtatFinPlaco): static
    {
        $this->EtatFinPlaco = $EtatFinPlaco;

        return $this;
    }

    public function getCloison(): ?int
    {
        return $this->Cloison;
    }

    public function setCloison(?int $Cloison): static
    {
        $this->Cloison = $Cloison;

        return $this;
    }

    public function getEtatCloison(): ?string
    {
        return $this->EtatCloison;
    }

    public function setEtatCloison(?string $EtatCloison): static
    {
        $this->EtatCloison = $EtatCloison;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setPlaco(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getPlaco() !== $this) {
            $chantier->setPlaco($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Plaquiste;
    }

    public function setArtisan(?Artisan $Plaquiste): static
    {
        $this->Plaquiste = $Plaquiste;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Plaquiste?->getId(), // Replace with the actual method to retrieve the artisan's name if needed
            ],
            'Préparation Placo' => [
                'nomAttribut' => 'PrepaPlaco',
                'week' => $this->PrepaPlaco,
                'etat' => $this->EtatPrepaPlaco,
            ],
            'Fin Placo' => [
                'nomAttribut' => 'FinPlaco',
                'week' => $this->FinPlaco,
                'etat' => $this->EtatFinPlaco,
            ],
            'Cloison' => [
                'nomAttribut' => 'Cloison',
                'week' => $this->Cloison,
                'etat' => $this->EtatCloison,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
