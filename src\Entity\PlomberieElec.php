<?php

namespace App\Entity;

use App\Repository\PlomberieElecRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlomberieElecRepository::class)]
class PlomberieElec
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'plomberieElecs')]
    private ?Artisan $PlombierElectricien = null;

    #[ORM\Column(nullable: true)]
    private ?int $PrepaDalle = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPrepaDalle = null;

    #[ORM\Column(nullable: true)]
    private ?int $PrepaFileriePlomberie = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPrepaFileriePlomberie = null;

    #[ORM\Column(nullable: true)]
    private ?int $GrosOeuvre = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatGrosOeuvre = null;

    #[ORM\Column(nullable: true)]
    private ?int $PosePACChaudiere = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatPosePACChaudiere = null;

    #[ORM\Column(nullable: true)]
    private ?int $FinitionElec = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatFinitionElec = null;

    #[ORM\Column(nullable: true)]
    private ?int $FinitionPlomberieSanitaire = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatFinitionPlomberieSanitaire = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'PlomberieElec', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlombierElectricien(): ?Artisan
    {
        return $this->PlombierElectricien;
    }

    public function setPlombierElectricien(?Artisan $PlombierElectricien): static
    {
        $this->PlombierElectricien = $PlombierElectricien;

        return $this;
    }

    public function getPrepaDalle(): ?int
    {
        return $this->PrepaDalle;
    }

    public function setPrepaDalle(?int $PrepaDalle): static
    {
        $this->PrepaDalle = $PrepaDalle;

        return $this;
    }

    public function getEtatPrepaDalle(): ?string
    {
        return $this->EtatPrepaDalle;
    }

    public function setEtatPrepaDalle(?string $EtatPrepaDalle): static
    {
        $this->EtatPrepaDalle = $EtatPrepaDalle;

        return $this;
    }

    public function getPrepaFileriePlomberie(): ?int
    {
        return $this->PrepaFileriePlomberie;
    }

    public function setPrepaFileriePlomberie(?int $PrepaFileriePlomberie): static
    {
        $this->PrepaFileriePlomberie = $PrepaFileriePlomberie;

        return $this;
    }

    public function getEtatPrepaFileriePlomberie(): ?string
    {
        return $this->EtatPrepaFileriePlomberie;
    }

    public function setEtatPrepaFileriePlomberie(?string $EtatPrepaFileriePlomberie): static
    {
        $this->EtatPrepaFileriePlomberie = $EtatPrepaFileriePlomberie;

        return $this;
    }

    public function getGrosOeuvre(): ?int
    {
        return $this->GrosOeuvre;
    }

    public function setGrosOeuvre(?int $GrosOeuvre): static
    {
        $this->GrosOeuvre = $GrosOeuvre;

        return $this;
    }

    public function getEtatGrosOeuvre(): ?string
    {
        return $this->EtatGrosOeuvre;
    }

    public function setEtatGrosOeuvre(?string $EtatGrosOeuvre): static
    {
        $this->EtatGrosOeuvre = $EtatGrosOeuvre;

        return $this;
    }

    public function getPosePACChaudiere(): ?int
    {
        return $this->PosePACChaudiere;
    }

    public function setPosePACChaudiere(?int $PosePACChaudiere): static
    {
        $this->PosePACChaudiere = $PosePACChaudiere;

        return $this;
    }

    public function getEtatPosePACChaudiere(): ?string
    {
        return $this->EtatPosePACChaudiere;
    }

    public function setEtatPosePACChaudiere(?string $EtatPosePACChaudiere): static
    {
        $this->EtatPosePACChaudiere = $EtatPosePACChaudiere;

        return $this;
    }

    public function getFinitionElec(): ?int
    {
        return $this->FinitionElec;
    }

    public function setFinitionElec(?int $FinitionElec): static
    {
        $this->FinitionElec = $FinitionElec;

        return $this;
    }

    public function getEtatFinitionElec(): ?string
    {
        return $this->EtatFinitionElec;
    }

    public function setEtatFinitionElec(?string $EtatFinitionElec): static
    {
        $this->EtatFinitionElec = $EtatFinitionElec;

        return $this;
    }

    public function getFinitionPlomberieSanitaire(): ?int
    {
        return $this->FinitionPlomberieSanitaire;
    }

    public function setFinitionPlomberieSanitaire(?int $FinitionPlomberieSanitaire): static
    {
        $this->FinitionPlomberieSanitaire = $FinitionPlomberieSanitaire;

        return $this;
    }

    public function getEtatFinitionPlomberieSanitaire(): ?string
    {
        return $this->EtatFinitionPlomberieSanitaire;
    }

    public function setEtatFinitionPlomberieSanitaire(?string $EtatFinitionPlomberieSanitaire): static
    {
        $this->EtatFinitionPlomberieSanitaire = $EtatFinitionPlomberieSanitaire;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setPlomberieElec(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getPlomberieElec() !== $this) {
            $chantier->setPlomberieElec($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->PlombierElectricien;
    }

    public function setArtisan(?Artisan $PlombierElectricien): static
    {
        $this->PlombierElectricien = $PlombierElectricien;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->PlombierElectricien?->getId(), // Replace with a method to retrieve the artisan's name if needed
            ],
            'Préparation Dalle' => [
                'nomAttribut' => 'PrepaDalle',
                'week' => $this->PrepaDalle,
                'etat' => $this->EtatPrepaDalle,
            ],
            'Préparation Filerie Plomberie' => [
                'nomAttribut' => 'PrepaFileriePlomberie',
                'week' => $this->PrepaFileriePlomberie,
                'etat' => $this->EtatPrepaFileriePlomberie,
            ],
            'Gros Œuvre' => [
                'nomAttribut' => 'GrosOeuvre',
                'week' => $this->GrosOeuvre,
                'etat' => $this->EtatGrosOeuvre,
            ],
            'Pose PAC / Chaudière' => [
                'nomAttribut' => 'PosePACChaudiere',
                'week' => $this->PosePACChaudiere,
                'etat' => $this->EtatPosePACChaudiere,
            ],
            'Finition Électricité' => [
                'nomAttribut' => 'FinitionElec',
                'week' => $this->FinitionElec,
                'etat' => $this->EtatFinitionElec,
            ],
            'Finition Plomberie Sanitaire' => [
                'nomAttribut' => 'FinitionPlomberieSanitaire',
                'week' => $this->FinitionPlomberieSanitaire,
                'etat' => $this->EtatFinitionPlomberieSanitaire,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
