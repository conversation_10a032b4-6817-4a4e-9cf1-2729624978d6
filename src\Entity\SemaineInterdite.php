<?php

namespace App\Entity;

use App\Repository\SemaineInterditeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SemaineInterditeRepository::class)]
class SemaineInterdite
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    private ?int $numeroSemaine = null;

    #[ORM\Column]
    private ?int $annee = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $motif = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $dateCreation = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $creePar = null;

    public function __construct()
    {
        $this->dateCreation = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNumeroSemaine(): ?int
    {
        return $this->numeroSemaine;
    }

    public function setNumeroSemaine(int $numeroSemaine): static
    {
        $this->numeroSemaine = $numeroSemaine;

        return $this;
    }

    public function getAnnee(): ?int
    {
        return $this->annee;
    }

    public function setAnnee(int $annee): static
    {
        $this->annee = $annee;

        return $this;
    }

    public function getMotif(): ?string
    {
        return $this->motif;
    }

    public function setMotif(?string $motif): static
    {
        $this->motif = $motif;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): static
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getCreePar(): ?string
    {
        return $this->creePar;
    }

    public function setCreePar(?string $creePar): static
    {
        $this->creePar = $creePar;

        return $this;
    }

    /**
     * Retourne une représentation lisible de la semaine
     */
    public function getSemaineFormatee(): string
    {
        return "Semaine {$this->numeroSemaine} - {$this->annee}";
    }

    /**
     * Retourne les dates de début et fin de la semaine
     */
    public function getDatesDebutFin(): array
    {
        $dateDebut = new \DateTime();
        $dateDebut->setISODate($this->annee, $this->numeroSemaine, 1); // Lundi
        
        $dateFin = clone $dateDebut;
        $dateFin->modify('+6 days'); // Dimanche
        
        return [
            'debut' => $dateDebut,
            'fin' => $dateFin
        ];
    }

    /**
     * Vérifie si cette semaine est dans le passé
     */
    public function estPassee(): bool
    {
        $maintenant = new \DateTime();
        $anneeCourante = (int) $maintenant->format('Y');
        $semaineCourante = (int) $maintenant->format('W');
        
        return ($this->annee < $anneeCourante) || 
               ($this->annee === $anneeCourante && $this->numeroSemaine < $semaineCourante);
    }

    /**
     * Retourne un identifiant unique pour cette semaine
     */
    public function getIdentifiantSemaine(): string
    {
        return $this->annee . '-' . str_pad($this->numeroSemaine, 2, '0', STR_PAD_LEFT);
    }
}
