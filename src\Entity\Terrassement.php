<?php

namespace App\Entity;

use App\Repository\TerrassementRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TerrassementRepository::class)]
class Terrassement
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'terrassements')]
    private ?Artisan $Terrassier = null;

    #[ORM\Column(nullable: true)]
    private ?int $Branchement = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatBranchement = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Terrassement', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTerrassier(): ?Artisan
    {
        return $this->Terrassier;
    }

    public function setTerrassier(?Artisan $Terrassier): static
    {
        $this->Terrassier = $Terrassier;

        return $this;
    }

    public function getBranchement(): ?int
    {
        return $this->Branchement;
    }

    public function setBranchement(?int $Branchement): static
    {
        $this->Branchement = $Branchement;

        return $this;
    }

    public function getEtatBranchement(): ?string
    {
        return $this->EtatBranchement;
    }

    public function setEtatBranchement(?string $EtatBranchement): static
    {
        $this->EtatBranchement = $EtatBranchement;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setTerrassement(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getTerrassement() !== $this) {
            $chantier->setTerrassement($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Terrassier;
    }

    public function setArtisan(?Artisan $artisan): static
    {
        $this->Terrassier = $artisan;

        return $this;
    }

    public function getEtapes(): array
{
    return [
        'Artisan' => [
            'Nom' => $this->Terrassier?->getId(), // Replace with a method to retrieve the artisan's name if needed
        ],
        'Branchement' => [
            'nomAttribut' => 'Branchement',
            'week' => $this->Branchement,
            'etat' => $this->EtatBranchement,
        ],
        'Observation' => $this->Observation,
    ];
}

}
