<?php

namespace App\Entity;

use App\Repository\ZinguerieRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ZinguerieRepository::class)]
class Zinguerie
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'zingueries')]
    private ?Artisan $Artisan = null;

    #[ORM\Column(nullable: true)]
    private ?int $Goutiere = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatGoutiere = null;

    #[ORM\Column(nullable: true)]
    private ?int $Couvertine = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatCouvertine = null;

    #[ORM\Column(nullable: true)]
    private ?int $Descente = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $EtatDescente = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Observation = null;

    #[ORM\OneToOne(mappedBy: 'Zingurie', cascade: ['persist', 'remove'])]
    private ?Chantier $chantier = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getArtisan(): ?Artisan
    {
        return $this->Artisan;
    }

    public function setArtisan(?Artisan $Artisan): static
    {
        $this->Artisan = $Artisan;

        return $this;
    }

    public function getGoutiere(): ?int
    {
        return $this->Goutiere;
    }

    public function setGoutiere(?int $Goutiere): static
    {
        $this->Goutiere = $Goutiere;

        return $this;
    }

    public function getEtatGoutiere(): ?string
    {
        return $this->EtatGoutiere;
    }

    public function setEtatGoutiere(?string $EtatGoutiere): static
    {
        $this->EtatGoutiere = $EtatGoutiere;

        return $this;
    }

    public function getCouvertine(): ?int
    {
        return $this->Couvertine;
    }

    public function setCouvertine(?int $Couvertine): static
    {
        $this->Couvertine = $Couvertine;

        return $this;
    }

    public function getEtatCouvertine(): ?string
    {
        return $this->EtatCouvertine;
    }

    public function setEtatCouvertine(?string $EtatCouvertine): static
    {
        $this->EtatCouvertine = $EtatCouvertine;

        return $this;
    }

    public function getDescente(): ?int
    {
        return $this->Descente;
    }

    public function setDescente(?int $Descente): static
    {
        $this->Descente = $Descente;

        return $this;
    }

    public function getEtatDescente(): ?string
    {
        return $this->EtatDescente;
    }

    public function setEtatDescente(?string $EtatDescente): static
    {
        $this->EtatDescente = $EtatDescente;

        return $this;
    }

    public function getObservation(): ?string
    {
        return $this->Observation;
    }

    public function setObservation(?string $Observation): static
    {
        $this->Observation = $Observation;

        return $this;
    }

    public function getChantier(): ?Chantier
    {
        return $this->chantier;
    }

    public function setChantier(?Chantier $chantier): static
    {
        // unset the owning side of the relation if necessary
        if ($chantier === null && $this->chantier !== null) {
            $this->chantier->setZingurie(null);
        }

        // set the owning side of the relation if necessary
        if ($chantier !== null && $chantier->getZingurie() !== $this) {
            $chantier->setZingurie($this);
        }

        $this->chantier = $chantier;

        return $this;
    }

    public function getEtapes(): array
    {
        return [
            'Artisan' => [
                'Nom' => $this->Artisan?->getId(), // Replace with a method to retrieve the artisan's name if needed
            ],
            'Goutière' => [
                'week' => $this->Goutiere,
                'etat' => $this->EtatGoutiere,
            ],
            'Couvertine' => [
                'week' => $this->Couvertine,
                'etat' => $this->EtatCouvertine,
            ],
            'Descente' => [
                'week' => $this->Descente,
                'etat' => $this->EtatDescente,
            ],
            'Observation' => $this->Observation,
        ];
    }

}
