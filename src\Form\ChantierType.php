<?php

namespace App\Form;

use App\Entity\Carrelage;
use App\Entity\Chantier;
use App\Entity\Chappe;
use App\Entity\Charpente;
use App\Entity\Enduit;
use App\Entity\Escalier;
use App\Entity\Etancheite;
use App\Entity\Finition;
use App\Entity\LaineSouffle;
use App\Entity\Maconnerie;
use App\Entity\Menuiserie;
use App\Entity\Peinture;
use App\Entity\Permeabilite;
use App\Entity\Placo;
use App\Entity\PlomberieElec;
use App\Entity\Terrassement;
use App\Entity\EntretientPAC;
use App\Entity\Zinguerie;
use App\Entity\Artisan;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;

class ChantierType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $attr_select = ['class' => 'selectpicker','data-live-search' => 'true','data-style' => 'btn border rounded-3 focus-purple', 'data-size' => '5', 'data-width' => '100%'];
        $builder
            ->add('Client', TextType::class, [
                'label' => 'Client',
                'attr' => ['placeholder' => 'Client'],
            ])
            ->add('Commune', TextType::class, [
                'label' => 'Commune',
                'attr' => ['placeholder' => 'Commune'],
            ])
            ->add('CDT', TextType::class, [
                'label' => 'Conducteur de travaux',
                'attr' => ['placeholder' => 'Conducteur de travaux'],
            ])
            ->add('DateDeb', DateType::class, [
                'widget' => 'single_text',
                'label' => 'Date de début',
            ])
            ->add('DateFin', DateType::class, [
                'widget' => 'single_text',
                'label' => 'Date de fin',
            ])
            ->add('Carrelage', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Carrelage')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Carrelage',
            ])
            ->add('Chappe', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Chappe')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Chappe',
            ])
            ->add('Charpente', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Charpente')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Charpente',
            ])
            ->add('Enduit', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Enduit')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Enduit',
            ])
            ->add('EntretientPAC', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'EntretientPAC')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Entretient PAC',
            ])
            ->add('Escalier', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Escalier')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Escalier',
            ])
            ->add('Etancheite', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Etancheite')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Etanchéité',
            ])
            ->add('Finition', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Finition')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Finition',
            ])
            ->add('LaineSouffle', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'LaineSouffle')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Laine soufflée',
            ])
            ->add('Maconnerie', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Maconnerie')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Maçonnerie',
            ])
            ->add('Menuiserie', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Menuiserie')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Menuiserie',
            ])
            ->add('Peinture', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Peinture')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Peinture',
            ])
            ->add('Permeabilite', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Permeabilite')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Perméabilité',
            ])
            ->add('Placo', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Placo')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Placo',
            ])
            ->add('PlomberieElec', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'PlomberieElec')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Plomberie / Elec',
            ])
            ->add('Terrassement', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Terrassement')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Terrassement',
            ])
            ->add('Zingurie', EntityType::class, [
                'class' => Artisan::class,
                'choice_label' => 'Nom',
                'attr' => $attr_select,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('a')
                        ->andWhere('a.Type = :val')
                        ->setParameter('val', 'Zinguerie')
                        ->orderBy('a.Nom', 'ASC');
                },
                'placeholder' => 'Sélectionnez un artisan',
                'required' => false,
                'mapped' => false,
                'label' => 'Zinguerie',
            ])
            ->add('delais', IntegerType::class, [
                'label' => 'Délais',
                'mapped' => false,
                'attr' => ['min' => 0,'placeholder' => 'Mois'],
                
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Chantier::class,
        ]);
    }
}
