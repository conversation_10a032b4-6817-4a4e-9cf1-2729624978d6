<?php

namespace App\Repository;

use App\Entity\Chantier;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Chantier>
 */
class ChantierRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Chantier::class);
    }

    public function findAllCDTDistinct(): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.CDT')
            ->distinct()
            ->getQuery()
            ->getResult();
    }

    public function findByChantierCountByConducteur(): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.CDT as conducteur, COUNT(c.id) as total')
            ->where('c.archive = 0 OR c.archive IS NULL')
            ->groupBy('c.CDT')
            ->getQuery()
            ->getResult();
    }

    public function findByAverageDelays(): array
    {
        $values = $this->createQueryBuilder('c')
            ->select("SUBSTRING(c.DateDeb, 1, 4) as annee, c.CDT as conducteur, AVG(DATE_DIFF(c.dateReel, c.DateDeb) / 30) as avgReel")
            ->where('c.DateDeb IS NOT NULL')
            ->andWhere('c.dateReel IS NOT NULL')
            ->groupBy('annee, conducteur')
            ->orderBy('annee', 'ASC')
            ->getQuery()
            ->getResult();

        foreach ($values as &$value) {
            $value['avgReel'] = round($value['avgReel'], 1);
        }
        return $values;
    }

    public function findByChantierCountByYear(): array
    {
        return $this->createQueryBuilder('c')
            ->select('SUBSTRING(c.DateDeb, 1, 4) as annee, COUNT(c.id) as total')
            ->where('c.DateDeb IS NOT NULL')
            ->groupBy('annee')
            ->orderBy('annee', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findByChantierCountByStatus(): array
    {
        // Chantiers en cours (non archivés)
        $enCours = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->where('c.archive = 0 OR c.archive IS NULL')
            ->getQuery()
            ->getSingleScalarResult();

        // Chantiers archivés
        $archives = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->where('c.archive = 1')
            ->getQuery()
            ->getSingleScalarResult();

        return [
            ['status' => 'En cours', 'total' => $enCours],
            ['status' => 'Archivés', 'total' => $archives]
        ];
    }

    public function findGlobalStats(): array
    {
        // Nombre total de chantiers
        $totalChantiers = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->getQuery()
            ->getSingleScalarResult();

        // Nombre de chantiers en cours
        $chantiersEnCours = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->where('c.archive = 0 OR c.archive IS NULL')
            ->getQuery()
            ->getSingleScalarResult();

        // Nombre de chantiers archivés
        $chantiersArchives = $this->createQueryBuilder('c')
            ->select('COUNT(c.id) as total')
            ->where('c.archive = 1')
            ->getQuery()
            ->getSingleScalarResult();

        // Durée moyenne globale
        $avgDuration = $this->createQueryBuilder('c')
            ->select('AVG(DATE_DIFF(c.dateReel, c.DateDeb) / 30) as avgDuration')
            ->where('c.DateDeb IS NOT NULL')
            ->andWhere('c.dateReel IS NOT NULL')
            ->getQuery()
            ->getSingleScalarResult();

        // Chantier le plus rapide
        $fastestProject = $this->createQueryBuilder('c')
            ->select('c.Client as client, c.Commune as commune, DATE_DIFF(c.dateReel, c.DateDeb) / 30 as duration')
            ->where('c.DateDeb IS NOT NULL')
            ->andWhere('c.dateReel IS NOT NULL')
            ->orderBy('duration', 'ASC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        // Chantier le plus long
        $longestProject = $this->createQueryBuilder('c')
            ->select('c.Client as client, c.Commune as commune, DATE_DIFF(c.dateReel, c.DateDeb) / 30 as duration')
            ->where('c.DateDeb IS NOT NULL')
            ->andWhere('c.dateReel IS NOT NULL')
            ->orderBy('duration', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        return [
            'totalChantiers' => $totalChantiers,
            'chantiersEnCours' => $chantiersEnCours,
            'chantiersArchives' => $chantiersArchives,
            'avgDuration' => round($avgDuration, 1),
            'fastestProject' => $fastestProject ? [
                'client' => $fastestProject['client'],
                'commune' => $fastestProject['commune'],
                'duration' => round($fastestProject['duration'], 1)
            ] : null,
            'longestProject' => $longestProject ? [
                'client' => $longestProject['client'],
                'commune' => $longestProject['commune'],
                'duration' => round($longestProject['duration'], 1)
            ] : null
        ];
    }

    public function findByChantierCountByCommune(): array
    {
        $results = $this->createQueryBuilder('c')
            ->select('c.Commune, COUNT(c.id) as total')
            ->where('c.archive = 0 OR c.archive IS NULL')
            ->groupBy('c.Commune')
            ->orderBy('c.Commune', 'ASC')
            ->getQuery()
            ->getResult();

        $grouped = [];
        $threshold = 2; // seuil de similarité (ajustable)

        foreach ($results as $row) {
            $original = $row['Commune'];
            // Normalisation de base
            $normalized = iconv('UTF-8', 'ASCII//TRANSLIT', $original);
            $normalized = preg_replace('/[^a-zA-Z0-9]/', '', $normalized);
            $normalized = strtolower($normalized);

            // Vérification si on peut regrouper avec une commune déjà existante
            $found = false;
            foreach ($grouped as $key => $group) {
                // Calcul de la distance entre la clé normalisée du groupe et la commune actuelle
                $distance = levenshtein($key, $normalized);
                if ($distance <= $threshold) {
                    // On regroupe : on peut aussi choisir de conserver le libellé le plus fréquent ou le premier rencontré
                    $grouped[$key]['total'] += $row['total'];
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                // Création d'un nouveau groupe
                $grouped[$normalized] = ['label' => $original, 'total' => $row['total']];
            }
        }

        return array_values($grouped);
    }



    //    /**
    //     * @return Chantier[] Returns an array of Chantier objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('c')
    //            ->andWhere('c.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('c.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Chantier
    //    {
    //        return $this->createQueryBuilder('c')
    //            ->andWhere('c.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
