<?php

namespace App\Repository;

use App\Entity\SemaineInterdite;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SemaineInterdite>
 */
class SemaineInterditeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SemaineInterdite::class);
    }

    /**
     * Trouve toutes les semaines interdites pour une année donnée
     */
    public function findByAnnee(int $annee): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.annee = :annee')
            ->setParameter('annee', $annee)
            ->orderBy('s.numeroSemaine', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve toutes les semaines interdites à partir d'une date donnée
     */
    public function findFuturesSemaines(\DateTime $dateDebut = null): array
    {
        if ($dateDebut === null) {
            $dateDebut = new \DateTime();
        }
        
        $anneeCourante = (int) $dateDebut->format('Y');
        $semaineCourante = (int) $dateDebut->format('W');

        return $this->createQueryBuilder('s')
            ->where('s.annee > :annee')
            ->orWhere('s.annee = :annee AND s.numeroSemaine >= :semaine')
            ->setParameter('annee', $anneeCourante)
            ->setParameter('semaine', $semaineCourante)
            ->orderBy('s.annee', 'ASC')
            ->addOrderBy('s.numeroSemaine', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Vérifie si une semaine spécifique est interdite
     */
    public function isSemaineInterdite(int $numeroSemaine, int $annee): bool
    {
        $result = $this->createQueryBuilder('s')
            ->select('COUNT(s.id)')
            ->andWhere('s.numeroSemaine = :numeroSemaine')
            ->andWhere('s.annee = :annee')
            ->setParameter('numeroSemaine', $numeroSemaine)
            ->setParameter('annee', $annee)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }

    /**
     * Trouve une semaine interdite spécifique
     */
    public function findBySemaineEtAnnee(int $numeroSemaine, int $annee): ?SemaineInterdite
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.numeroSemaine = :numeroSemaine')
            ->andWhere('s.annee = :annee')
            ->setParameter('numeroSemaine', $numeroSemaine)
            ->setParameter('annee', $annee)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Retourne toutes les semaines interdites sous forme de tableau associatif
     * pour optimiser les vérifications
     */
    public function getSemainesInterditesPourCalcul(): array
    {
        $semaines = $this->createQueryBuilder('s')
            ->select('s.numeroSemaine, s.annee')
            ->getQuery()
            ->getResult();

        $semainesInterdites = [];
        foreach ($semaines as $semaine) {
            $key = $semaine['annee'] . '-' . str_pad($semaine['numeroSemaine'], 2, '0', STR_PAD_LEFT);
            $semainesInterdites[$key] = true;
        }

        return $semainesInterdites;
    }

    /**
     * Supprime les semaines interdites passées (optionnel, pour maintenance)
     */
    public function supprimerSemainesPassees(): int
    {
        $maintenant = new \DateTime();
        $anneeCourante = (int) $maintenant->format('Y');
        $semaineCourante = (int) $maintenant->format('W');

        return $this->createQueryBuilder('s')
            ->delete()
            ->where('s.annee < :annee')
            ->orWhere('s.annee = :annee AND s.numeroSemaine < :semaine')
            ->setParameter('annee', $anneeCourante)
            ->setParameter('semaine', $semaineCourante)
            ->getQuery()
            ->execute();
    }

    /**
     * Trouve les années qui ont des semaines interdites
     */
    public function getAnneesAvecSemainesInterdites(): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('DISTINCT s.annee')
            ->orderBy('s.annee', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($result, 'annee');
    }
}
