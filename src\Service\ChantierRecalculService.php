<?php

namespace App\Service;

use App\Repository\ChantierRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ChantierRecalculService
{
    private ChantierRepository $chantierRepository;
    private SemaineCalculatorService $semaineCalculatorService;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    // Définition des étapes avec leurs décalages et états (exactement comme dans ChantierController)
    const ORDRE_CHANTIER = [
        ['key' => 'Fondation', 'value' => 'EtatFondation'],
        ['key' => 'Dalle', 'value' => 'EtatDalle'],
        ['key' => 'PrepaDalle', 'value' => 'EtatPrepaDalle'],
        ['key' => 'Branchement', 'value' => 'EtatBranchement'],
        ['key' => 'Elevation', 'value' => 'EtatElevation'],
        ['key' => 'Rampannage', 'value' => 'EtatRampannage'],
        ['key' => 'ReleveSeuil', 'value' => 'EtatReleveSeuil'],
        ['key' => 'Seuil', 'value' => 'EtatSeuil'],
        ['key' => 'Charpentes', 'value' => 'EtatCharpente'],
        ['key' => 'Couverture', 'value' => 'EtatCouverture'],
        ['key' => 'EtancheiteIntervention', 'value' => 'EtatEtancheiteIntervention'],
        ['key' => 'Couvertine', 'value' => 'EtatCouvertine'],
        ['key' => 'Goutiere', 'value' => 'EtatGoutiere'],
        ['key' => 'PoseMenuiserie', 'value' => 'EtatPoseMenuiserie'],
        ['key' => 'PrepaPlaco', 'value' => 'EtatPrepaPlaco'],
        ['key' => 'PrepaFileriePlomberie', 'value' => 'EtatPrepaFileriePlomberie'],
        ['key' => 'FinPlaco', 'value' => 'EtatFinPlaco'],
        ['key' => 'GrosOeuvre', 'value' => 'EtatGrosOeuvre'],
        ['key' => 'FinitionElec', 'value' => 'EtatFinitionElec'],
        ['key' => 'ChappeIntervention', 'value' => 'EtatChappeIntervention'],
        ['key' => 'PosePACChaudiere', 'value' => 'EtatPosePACChaudiere'],
        ['key' => 'MES', 'value' => 'EtatMES'],
        ['key' => 'Ravalement', 'value' => 'EtatRavalement'],
        ['key' => 'Descente', 'value' => 'EtatDescente'],
        ['key' => 'LaineSouffleIntervention', 'value' => 'EtatLaineSouffleIntervention'],
        ['key' => 'PoseCarrelage', 'value' => 'EtatPoseCarrelage'],
        ['key' => 'PoseEscalier', 'value' => 'EtatPoseEscalier'],
        ['key' => 'Cloison', 'value' => 'EtatCloison'],
        ['key' => 'FinitionPlomberieSanitaire', 'value' => 'EtatFinitionPlomberieSanitaire'],
        ['key' => 'Finitions', 'value' => 'EtatFinition'],
        ['key' => 'PosePlinthe', 'value' => 'EtatPosePlinthe'],
        ['key' => 'PeintureIntervention', 'value' => 'EtatPeinture'],
        ['key' => 'PermeabiliteIntervention', 'value' => 'EtatPermeabiliteIntervention'],
    ];

    const NUM_SEMAINE = [
        "Fondation" => 0,
        "Dalle" => 3,
        "PrepaDalle" => 3,
        "Branchement" => 4,
        "Elevation" => 7,
        "Rampannage" => 8,
        "ReleveSeuil" => 8,
        "Seuil" => 12,
        'Charpentes' => 9,
        "Couverture" => 9,
        "EtancheiteIntervention" => 10,
        "Couvertine" => 11,
        "Goutiere" => 11,
        "PoseMenuiserie" => 11,
        "PrepaPlaco" => 12,
        "PrepaFileriePlomberie" => 13,
        "FinPlaco" => 15,
        "FinitionElec" => 16,
        "GrosOeuvre" => 17,
        "ChappeIntervention" => 18,
        "PosePACChaudiere" => 19,
        "MES" => 20,
        "Ravalement" => 21,
        "Descente" => 22,
        "LaineSouffleIntervention" => 22,
        "PoseCarrelage" => 23,
        "PoseEscalier" => 25,
        "Cloison" => 27,
        "FinitionPlomberieSanitaire" => 24,
        "Finitions" => 25,
        "PosePlinthe" => 27,
        "PeintureIntervention" => 27,
        "PermeabiliteIntervention" => 27,
    ];

    public function __construct(
        ChantierRepository $chantierRepository,
        SemaineCalculatorService $semaineCalculatorService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->chantierRepository = $chantierRepository;
        $this->semaineCalculatorService = $semaineCalculatorService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Recalcule tous les chantiers en cours après ajout/modification de semaines interdites
     */
    public function recalculerTousLesChantiersEnCours(): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        // Récupérer tous les chantiers en cours
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersEnCours as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        $this->logger->info('Recalcul des chantiers terminé', $resultats);
        
        return $resultats;
    }

    /**
     * Recalcule un chantier spécifique
     */
    public function recalculerChantier($chantier): array
    {
        return $this->semaineCalculatorService->recalculerChantier($chantier, self::ORDRE_CHANTIER, self::NUM_SEMAINE);
    }

    /**
     * Vérifie si une étape peut être recalculée (pas déjà terminée)
     */
    private function peutEtreRecalculee($chantier, string $etapeKey, string $etatKey): bool
    {
        // Vérifier que les méthodes existent
        if (!method_exists($chantier, 'get' . $etapeKey) || !method_exists($chantier, 'get' . $etatKey)) {
            return false;
        }

        // Récupérer l'état de l'étape
        $etat = $chantier->{'get' . $etatKey}();

        // Ne pas recalculer si l'étape est terminée (FAIT)
        if ($etat === 'FAIT') {
            return false;
        }

        return true;
    }

    /**
     * Recalcule seulement les chantiers qui ont des étapes dans les semaines interdites ajoutées
     */
    public function recalculerChantiersAffectes(array $semainesInterdites): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        $chantiersAffectes = [];
        
        foreach ($chantiersEnCours as $chantier) {
            $estAffecte = false;
            
            // Vérifier si le chantier a des étapes dans les semaines interdites
            foreach (self::ORDRE_CHANTIER as $etapeInfo) {
                $etape = $etapeInfo['key'];
                $etat = $etapeInfo['value'];

                // Vérifier que la méthode existe et que l'étape peut être recalculée
                if ($this->peutEtreRecalculee($chantier, $etape, $etat)) {
                    $semaineEtape = $chantier->{'get' . $etape}();
                    if ($semaineEtape) {
                        $anneeChantier = (int) $chantier->getDateDeb()->format('Y');
                        $keyEtape = $anneeChantier . '-' . str_pad($semaineEtape, 2, '0', STR_PAD_LEFT);

                        if (in_array($keyEtape, $semainesInterdites)) {
                            $estAffecte = true;
                            break;
                        }
                    }
                }
            }
            
            if ($estAffecte) {
                $chantiersAffectes[] = $chantier;
            }
        }
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersAffectes as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier affecté ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        return $resultats;
    }
}
