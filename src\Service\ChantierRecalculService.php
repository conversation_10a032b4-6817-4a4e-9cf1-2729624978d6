<?php

namespace App\Service;

use App\Repository\ChantierRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ChantierRecalculService
{
    private ChantierRepository $chantierRepository;
    private SemaineCalculatorService $semaineCalculatorService;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    // Définition des étapes et leurs décalages (copié depuis ChantierController)
    const ETAPES_DECALAGES = [
        "Fondation" => 0,
        "Dalle" => 3,
        "PrepaDalle" => 3,
        "Branchement" => 4,
        "Elevation" => 7,
        "Rampannage" => 8,
        "ReleveSeuil" => 8,
        "Seuil" => 12,
        'Charpentes' => 9,
        "Couverture" => 9,
        "EtancheiteIntervention" => 10,
        "Couvertine" => 11,
        "Goutiere" => 11,
        "PoseMenuiserie" => 11,
        "PrepaPlaco" => 12,
        "PrepaFileriePlomberie" => 13,
        "FinPlaco" => 15,
        "GrosOeuvre" => 16,
        "FinitionElec" => 17,
        "ChappeIntervention" => 18,
        "CarrelageIntervention" => 19,
        "EnduitIntervention" => 20,
        "PeinturePapierPeint" => 21,
        "LaineSouffleIntervention" => 22,
        "FinitionIntervention" => 23,
        "PoseEscalier" => 24,
        "Cloison" => 25,
        "Nettoyage" => 26,
        "Livraison" => 27
    ];

    public function __construct(
        ChantierRepository $chantierRepository,
        SemaineCalculatorService $semaineCalculatorService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->chantierRepository = $chantierRepository;
        $this->semaineCalculatorService = $semaineCalculatorService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Recalcule tous les chantiers en cours après ajout/modification de semaines interdites
     */
    public function recalculerTousLesChantiersEnCours(): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        // Récupérer tous les chantiers en cours
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersEnCours as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        $this->logger->info('Recalcul des chantiers terminé', $resultats);
        
        return $resultats;
    }

    /**
     * Recalcule un chantier spécifique
     */
    public function recalculerChantier($chantier): array
    {
        return $this->semaineCalculatorService->recalculerChantier($chantier, self::ETAPES_DECALAGES);
    }

    /**
     * Recalcule seulement les chantiers qui ont des étapes dans les semaines interdites ajoutées
     */
    public function recalculerChantiersAffectes(array $semainesInterdites): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        $chantiersAffectes = [];
        
        foreach ($chantiersEnCours as $chantier) {
            $estAffecte = false;
            
            // Vérifier si le chantier a des étapes dans les semaines interdites
            foreach (self::ETAPES_DECALAGES as $etape => $decalage) {
                $semaineEtape = $chantier->{'get' . $etape}();
                if ($semaineEtape) {
                    $anneeChantier = (int) $chantier->getDateDeb()->format('Y');
                    $keyEtape = $anneeChantier . '-' . str_pad($semaineEtape, 2, '0', STR_PAD_LEFT);
                    
                    if (in_array($keyEtape, $semainesInterdites)) {
                        $estAffecte = true;
                        break;
                    }
                }
            }
            
            if ($estAffecte) {
                $chantiersAffectes[] = $chantier;
            }
        }
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersAffectes as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier affecté ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        return $resultats;
    }
}
