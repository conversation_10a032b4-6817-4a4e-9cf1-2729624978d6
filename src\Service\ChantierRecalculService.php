<?php

namespace App\Service;

use App\Repository\ChantierRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ChantierRecalculService
{
    private ChantierRepository $chantierRepository;
    private SemaineCalculatorService $semaineCalculatorService;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    // Définition des étapes et leurs décalages (exactement comme dans ChantierController)
    const ETAPES_DECALAGES = [
        "Fondation" => 0,
        "Dalle" => 3,
        "PrepaDalle" => 3, // Correspond à 'Préparation dalle'
        "Branchement" => 4,
        "Elevation" => 7, // Correspond à 'Fin élèvation'
        "Rampannage" => 8,
        "ReleveSeuil" => 8, // Correspond à 'Relevés de seuils'
        "Seuil" => 12, // Correspond à 'Seuils'
        'Charpentes' => 9,
        "Couverture" => 9, // Correspond à 'Intervention charpente - couverture'
        "EtancheiteIntervention" => 10,
        "Couvertine" => 11, // Correspond à 'Pose couvertines'
        "Goutiere" => 11, // Correspond à 'Pose gouttiére'
        "PoseMenuiserie" => 11, // Correspond à 'Pose menuiseries'
        "PrepaPlaco" => 12, // Correspond à 'Préparation placo'
        "PrepaFileriePlomberie" => 13, // Correspond à 'Prépa Plomberie' et 'Préparation filerie'
        "FinPlaco" => 15,
        "FinitionElec" => 16, // Correspond à 'Finition electricité'
        "GrosOeuvre" => 17,
        "ChappeIntervention" => 18, // Correspond à 'Chape liquide'
        "PosePACChaudiere" => 19, // Correspond à 'Pose PAC et BAC'
        "MES" => 20,
        "Ravalement" => 21,
        "Descente" => 22, // Correspond à 'Pose descente'
        "LaineSouffleIntervention" => 22, // Correspond à 'ISOLATION Soufflée'
        "PoseCarrelage" => 23,
        "PoseEscalier" => 25,
        "Cloison" => 27, // Correspond à 'Cloison après carrelage'
        "FinitionPlomberieSanitaire" => 24, // Correspond à 'Finition Plomberie + Sanitaire'
        "Finitions" => 25, // Correspond à 'Finitions Intérieures'
        "PosePlinthe" => 27,
        "PeintureIntervention" => 27, // Correspond à 'Peinture'
        "PermeabiliteIntervention" => 27, // Correspond à 'Perméa'
    ];

    public function __construct(
        ChantierRepository $chantierRepository,
        SemaineCalculatorService $semaineCalculatorService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->chantierRepository = $chantierRepository;
        $this->semaineCalculatorService = $semaineCalculatorService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Recalcule tous les chantiers en cours après ajout/modification de semaines interdites
     */
    public function recalculerTousLesChantiersEnCours(): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        // Récupérer tous les chantiers en cours
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersEnCours as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        $this->logger->info('Recalcul des chantiers terminé', $resultats);
        
        return $resultats;
    }

    /**
     * Recalcule un chantier spécifique
     */
    public function recalculerChantier($chantier): array
    {
        return $this->semaineCalculatorService->recalculerChantier($chantier, self::ETAPES_DECALAGES);
    }

    /**
     * Recalcule seulement les chantiers qui ont des étapes dans les semaines interdites ajoutées
     */
    public function recalculerChantiersAffectes(array $semainesInterdites): array
    {
        // Recharger les semaines interdites dans le service
        $this->semaineCalculatorService->rechargerSemainesInterdites();
        
        $chantiersEnCours = $this->chantierRepository->findChantiersEnCours();
        $chantiersAffectes = [];
        
        foreach ($chantiersEnCours as $chantier) {
            $estAffecte = false;
            
            // Vérifier si le chantier a des étapes dans les semaines interdites
            foreach (self::ETAPES_DECALAGES as $etape => $decalage) {
                // Vérifier que la méthode existe avant de l'appeler
                if (method_exists($chantier, 'get' . $etape)) {
                    $semaineEtape = $chantier->{'get' . $etape}();
                    if ($semaineEtape) {
                        $anneeChantier = (int) $chantier->getDateDeb()->format('Y');
                        $keyEtape = $anneeChantier . '-' . str_pad($semaineEtape, 2, '0', STR_PAD_LEFT);

                        if (in_array($keyEtape, $semainesInterdites)) {
                            $estAffecte = true;
                            break;
                        }
                    }
                }
            }
            
            if ($estAffecte) {
                $chantiersAffectes[] = $chantier;
            }
        }
        
        $resultats = [
            'chantiersRecalcules' => 0,
            'chantiersModifies' => 0,
            'etapesTotalesModifiees' => 0,
            'details' => []
        ];

        foreach ($chantiersAffectes as $chantier) {
            try {
                $resultatChantier = $this->recalculerChantier($chantier);
                
                if (!empty($resultatChantier['etapesModifiees'])) {
                    $resultats['chantiersModifies']++;
                    $resultats['etapesTotalesModifiees'] += count($resultatChantier['etapesModifiees']);
                    
                    $resultats['details'][] = [
                        'chantierId' => $chantier->getId(),
                        'client' => $chantier->getClient(),
                        'commune' => $chantier->getCommune(),
                        'etapesModifiees' => $resultatChantier['etapesModifiees']
                    ];
                }
                
                $resultats['chantiersRecalcules']++;
                
            } catch (\Exception $e) {
                $this->logger->error('Erreur lors du recalcul du chantier affecté ' . $chantier->getId(), [
                    'error' => $e->getMessage(),
                    'chantier' => $chantier->getId()
                ]);
            }
        }

        // Sauvegarder toutes les modifications
        $this->entityManager->flush();
        
        return $resultats;
    }
}
