<?php

namespace App\Service;

use App\Repository\SemaineInterditeRepository;

class SemaineCalculatorService
{
    private SemaineInterditeRepository $semaineInterditeRepository;
    private array $semainesInterdites = [];

    public function __construct(SemaineInterditeRepository $semaineInterditeRepository)
    {
        $this->semaineInterditeRepository = $semaineInterditeRepository;
        $this->loadSemainesInterdites();
    }

    /**
     * Charge les semaines interdites en mémoire pour optimiser les calculs
     */
    private function loadSemainesInterdites(): void
    {
        $this->semainesInterdites = $this->semaineInterditeRepository->getSemainesInterditesPourCalcul();
    }

    /**
     * Calcule une semaine en évitant les semaines interdites
     */
    public function calculerSemaineAvecInterdites(int $semaineDebut, int $anneeDebut, int $decalage): array
    {
        $semaineCalculee = $semaineDebut;
        $anneeCalculee = $anneeDebut;
        $semainesEvitees = [];
        
        // Appliquer le décalage en évitant les semaines interdites
        for ($i = 0; $i < $decalage; $i++) {
            do {
                $semaineCalculee++;
                if ($semaineCalculee > 52) {
                    $semaineCalculee = 1;
                    $anneeCalculee++;
                }
                
                $key = $anneeCalculee . '-' . str_pad($semaineCalculee, 2, '0', STR_PAD_LEFT);
                
                if (isset($this->semainesInterdites[$key])) {
                    $semainesEvitees[] = $key;
                }
            } while (isset($this->semainesInterdites[$key]));
        }

        return [
            'semaine' => $semaineCalculee,
            'annee' => $anneeCalculee,
            'semainesEvitees' => $semainesEvitees
        ];
    }

    /**
     * Calcule une semaine simple (méthode originale pour compatibilité)
     */
    public function calculerSemaineSimple(int $semaineDebut, int $decalage): int
    {
        $semaineCalculee = $semaineDebut + $decalage;
        
        if ($semaineCalculee > 52) {
            return $semaineCalculee % 52;
        }
        
        return $semaineCalculee;
    }

    /**
     * Vérifie si une semaine est interdite
     */
    public function isSemaineInterdite(int $numeroSemaine, int $annee): bool
    {
        $key = $annee . '-' . str_pad($numeroSemaine, 2, '0', STR_PAD_LEFT);
        return isset($this->semainesInterdites[$key]);
    }

    /**
     * Calcule toutes les étapes d'un chantier en évitant les semaines interdites
     */
    public function calculerEtapesChantier(\DateTime $dateDebut, array $etapesDecalages): array
    {
        $semaineDebut = (int) $dateDebut->format('W');
        $anneeDebut = (int) $dateDebut->format('Y');
        
        $etapesCalculees = [];
        
        foreach ($etapesDecalages as $etape => $decalage) {
            $resultat = $this->calculerSemaineAvecInterdites($semaineDebut, $anneeDebut, $decalage);
            
            $etapesCalculees[$etape] = [
                'semaine' => $resultat['semaine'],
                'annee' => $resultat['annee'],
                'semaineOriginale' => $this->calculerSemaineSimple($semaineDebut, $decalage),
                'semainesEvitees' => $resultat['semainesEvitees']
            ];
        }
        
        return $etapesCalculees;
    }

    /**
     * Calcule les étapes suivantes après modification d'une étape
     */
    public function calculerEtapesSuivantes(int $semaineModifiee, int $anneeModifiee, int $decalageOriginal, array $etapesSuivantes): array
    {
        $diff = $semaineModifiee - $decalageOriginal;
        $etapesRecalculees = [];
        
        foreach ($etapesSuivantes as $etape => $decalageOriginalEtape) {
            $nouveauDecalage = $decalageOriginalEtape + $diff;
            
            if ($nouveauDecalage > 0) {
                $resultat = $this->calculerSemaineAvecInterdites($semaineModifiee, $anneeModifiee, $nouveauDecalage - $decalageOriginal);
                
                $etapesRecalculees[$etape] = [
                    'semaine' => $resultat['semaine'],
                    'annee' => $resultat['annee'],
                    'semaineOriginale' => $this->calculerSemaineSimple($semaineModifiee, $nouveauDecalage - $decalageOriginal)
                ];
            }
        }
        
        return $etapesRecalculees;
    }

    /**
     * Recharge les semaines interdites (utile après modification)
     */
    public function rechargerSemainesInterdites(): void
    {
        $this->loadSemainesInterdites();
    }

    /**
     * Retourne la liste des semaines interdites
     */
    public function getSemainesInterdites(): array
    {
        return $this->semainesInterdites;
    }

    /**
     * Trouve la prochaine semaine disponible à partir d'une semaine donnée
     */
    public function prochaineSemaineDisponible(int $semaine, int $annee): array
    {
        while ($this->isSemaineInterdite($semaine, $annee)) {
            $semaine++;
            if ($semaine > 52) {
                $semaine = 1;
                $annee++;
            }
        }
        
        return ['semaine' => $semaine, 'annee' => $annee];
    }

    /**
     * Calcule le nombre de semaines interdites entre deux dates
     */
    public function compterSemainesInterdites(int $semaineDebut, int $anneeDebut, int $semaineFin, int $anneeFin): int
    {
        $count = 0;
        $semaine = $semaineDebut;
        $annee = $anneeDebut;

        while ($annee < $anneeFin || ($annee === $anneeFin && $semaine <= $semaineFin)) {
            if ($this->isSemaineInterdite($semaine, $annee)) {
                $count++;
            }

            $semaine++;
            if ($semaine > 52) {
                $semaine = 1;
                $annee++;
            }
        }

        return $count;
    }

    /**
     * Recalcule toutes les étapes d'un chantier en évitant les semaines interdites
     * Utilisé pour recalculer les chantiers existants après ajout de semaines interdites
     */
    public function recalculerChantier($chantier, array $etapesDecalages): array
    {
        if (!$chantier->getDateDeb()) {
            return [];
        }

        $dateDebut = $chantier->getDateDeb();
        $semaineDebut = (int) $dateDebut->format('W');
        $anneeDebut = (int) $dateDebut->format('Y');

        $etapesRecalculees = [];
        $etapesModifiees = [];

        foreach ($etapesDecalages as $etape => $decalage) {
            // Vérifier que les méthodes getter et setter existent
            if (!method_exists($chantier, 'get' . $etape) || !method_exists($chantier, 'set' . $etape)) {
                continue; // Ignorer cette étape si les méthodes n'existent pas
            }

            // Calculer la nouvelle semaine avec les semaines interdites
            $resultat = $this->calculerSemaineAvecInterdites($semaineDebut, $anneeDebut, $decalage);
            $nouvelleSemaine = $resultat['semaine'];

            // Récupérer l'ancienne semaine du chantier
            $ancienneSemaine = $chantier->{'get' . $etape}();

            // Si la semaine a changé, l'ajouter aux modifications
            if ($ancienneSemaine !== $nouvelleSemaine) {
                $etapesModifiees[$etape] = [
                    'ancienne' => $ancienneSemaine,
                    'nouvelle' => $nouvelleSemaine,
                    'annee' => $resultat['annee'],
                    'semainesEvitees' => $resultat['semainesEvitees']
                ];

                // Mettre à jour le chantier
                $chantier->{'set' . $etape}($nouvelleSemaine);
            }

            $etapesRecalculees[$etape] = [
                'semaine' => $nouvelleSemaine,
                'annee' => $resultat['annee'],
                'semaineOriginale' => $this->calculerSemaineSimple($semaineDebut, $decalage),
                'semainesEvitees' => $resultat['semainesEvitees']
            ];
        }

        return [
            'etapesRecalculees' => $etapesRecalculees,
            'etapesModifiees' => $etapesModifiees
        ];
    }
}
