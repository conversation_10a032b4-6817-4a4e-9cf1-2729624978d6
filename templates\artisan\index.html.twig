{% extends 'base.html.twig' %}

{% block title %}Artisans{% endblock %}

{% block body %}
{% set categories = {
    'Terrassement': '#996633',
    'Maconnerie': '#C00000',
    'PlomberieElec': '#00B0F0',
    'Charpente': '#009900',
    'Etancheite': '#008000',
    'Zinguerie': '#8497B0',
    'Menuiserie': '#000000',
    'Placo': '#CC99FF',
    'Chappe': '#00CC99',
    'EntretientPAC': 'rgb(101, 61, 13)',
    'Enduit': '#FFC000',
    'LaineSouffle': '#A50021',
    'Carrelage': '#ED7D31',
    'Escalier': '#A5A5A5',
    'Finition': '#3F888F',
    'Peinture': '#E4C6C9',
    'Permeabilite': '#9966FF'
} %}

<div class="container mt-4">
    <!-- Header avec titre et actions -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="fw-bold text-dark mb-1">
                        <i class="bi bi-people-fill me-2 text-primary"></i>Gestion des Artisans
                    </h3>
                    <p class="text-muted mb-0">Gérez vos artisans par corps de métier</p>
                </div>
                <div class="d-flex gap-3 align-items-center">
                    <!-- Barre de recherche -->
                    <div class="input-group" style="width: 300px;">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0 bg-light" id="searchArtisan" placeholder="Rechercher un artisan...">
                    </div>

                    <!-- Filtre par type -->
                    <select class="selectpicker" id="filtre-type" data-live-search="true" data-style="btn-outline-secondary" title="Filtrer par métier">
                        <option value="ALL" selected>Tous les métiers</option>
                        <option value="Terrassement" data-content="<span class='badge me-2' style='background: #996633;color: #996633; font-size: 0.7rem;'>●</span> Terrassement">Terrassement</option>
                        <option value="Maconnerie" data-content="<span class='badge me-2' style='background: #C00000;color: #C00000; font-size: 0.7rem;'>●</span> Maçonnerie">Maçonnerie</option>
                        <option value="PlomberieElec" data-content="<span class='badge me-2' style='background: #00B0F0;color: #00B0F0; font-size: 0.7rem;'>●</span> Plomberie Élec">Plomberie Élec</option>
                        <option value="Charpente" data-content="<span class='badge me-2' style='background: #009900;color: #009900; font-size: 0.7rem;'>●</span> Charpente">Charpente</option>
                        <option value="Etancheite" data-content="<span class='badge me-2' style='background: #008000;color: #008000; font-size: 0.7rem;'>●</span> Étanchéité">Étanchéité</option>
                        <option value="Zinguerie" data-content="<span class='badge me-2' style='background: #8497B0;color: #8497B0; font-size: 0.7rem;'>●</span> Zinguerie">Zinguerie</option>
                        <option value="Menuiserie" data-content="<span class='badge me-2' style='background: #000000;color: #000000; font-size: 0.7rem;'>●</span> Menuiserie">Menuiserie</option>
                        <option value="Placo" data-content="<span class='badge me-2' style='background: #CC99FF;color: #CC99FF; font-size: 0.7rem;'>●</span> Placo">Placo</option>
                        <option value="Chappe" data-content="<span class='badge me-2' style='background: #00CC99;color: #00CC99; font-size: 0.7rem;'>●</span> Chappe">Chappe</option>
                        <option value="EntretientPAC" data-content="<span class='badge me-2' style='background: rgb(101, 61, 13);color: rgb(101, 61, 13); font-size: 0.7rem;'>●</span> Entretient PAC">Entretient PAC</option>
                        <option value="Enduit" data-content="<span class='badge me-2' style='background: #FFC000;color: #FFC000; font-size: 0.7rem;'>●</span> Enduit">Enduit</option>
                        <option value="LaineSouffle" data-content="<span class='badge me-2' style='background: #A50021;color: #A50021; font-size: 0.7rem;'>●</span> Laine Soufflée">Laine Soufflée</option>
                        <option value="Carrelage" data-content="<span class='badge me-2' style='background: #ED7D31;color: #ED7D31; font-size: 0.7rem;'>●</span> Carrelage">Carrelage</option>
                        <option value="Escalier" data-content="<span class='badge me-2' style='background: #A5A5A5;color: #A5A5A5; font-size: 0.7rem;'>●</span> Escalier">Escalier</option>
                        <option value="Finition" data-content="<span class='badge me-2' style='background: #3F888F;color: #3F888F; font-size: 0.7rem;'>●</span> Finition">Finition</option>
                        <option value="Peinture" data-content="<span class='badge me-2' style='background: #E4C6C9;color: #E4C6C9; font-size: 0.7rem;'>●</span> Peinture">Peinture</option>
                        <option value="Permeabilite" data-content="<span class='badge me-2' style='background: #9966FF;color: #9966FF; font-size: 0.7rem;'>●</span> Perméabilité">Perméabilité</option>
                    </select>

                    <!-- Bouton d'ajout -->
                    <button class="btn btn-primary px-4" data-bs-toggle="modal" data-bs-target="#addArtisanModal">
                        <i class="bi bi-plus-circle me-2"></i>Nouvel Artisan
                    </button>
                </div>
            </div>
        </div>
    </div>
    <hr>
    <!-- Contenu principal -->
    <div class="row">
        {% for type, artisans in groupedArtisans %}
        <div class="col-12 mb-4 div-type" type="{{ type }}">
            <!-- En-tête de section -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle me-3 d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px; background: {{ categories[type] }}70;">
                                <span class="badge rounded-circle" style="background: {{ categories[type] }}; width: 12px; height: 12px;"></span>
                            </div>
                            <div>
                                <h5 class="mb-0 fw-bold text-dark">
                                    {% if type == 'Maconnerie' %}
                                        Maçonnerie
                                    {% elseif type == 'PlomberieElec' %}
                                        Plomberie Électricité Chauffage
                                    {% elseif type == 'Etancheite' %}
                                        Étanchéité
                                    {% elseif type == 'LaineSouffle' %}
                                        Laine Soufflée
                                    {% elseif type == 'Permeabilite' %}
                                        Perméabilité
                                    {% else %}
                                        {{ type }}
                                    {% endif %}
                                </h5>
                                <small class="text-muted">{{ artisans|length }} artisan{{ artisans|length > 1 ? 's' : '' }}</small>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addArtisanModal" onclick="$('#type').selectpicker('val', '{{ type }}')">
                            <i class="bi bi-plus me-1"></i>Ajouter
                        </button>
                    </div>
                </div>

                <!-- Liste des artisans -->
                <div class="card-body pt-0">
                    <div class="row g-3">
                        {% for artisan in artisans|sort((a, b) => b.getNbChantiers() - a.getNbChantiers()) %}
                        <div class="col-lg-4 col-md-6">
                            <div class="artisan-card border rounded-3 p-3 h-100 position-relative" id-artisan="{{ artisan.id }}">
                                <!-- Border left colorée -->
                                <div class="position-absolute top-0 start-0 h-100 rounded-start" style="width: 4px; background: {{ categories[type] }};"></div>

                                <!-- Mode affichage -->
                                <div class="artisan-display">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 fw-semibold text-dark artisan-name">{{ artisan.nom }}</h6>
                                            <small class="text-muted">
                                                <i class="bi bi-tools me-1"></i>
                                                {{ artisan.getNbChantiers }} chantier{{ artisan.getNbChantiers > 1 ? 's' : '' }}
                                            </small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item edit-artisan" href="#"><i class="bi bi-pencil me-2"></i>Modifier</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger delete-artisan" href="#"><i class="bi bi-trash me-2"></i>Supprimer</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mode édition -->
                                <div class="artisan-edit" style="display: none;">
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control artisan-input" value="{{ artisan.nom }}">
                                        <button class="btn btn-success save-artisan" type="button">
                                            <i class="bi bi-check"></i>
                                        </button>
                                        <button class="btn btn-secondary cancel-edit" type="button">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        <!-- Message si aucun artisan -->
                        {% if artisans|length == 0 %}
                        <div class="col-12 text-center py-4">
                            <i class="bi bi-person-plus display-4 text-muted mb-3"></i>
                            <h6 class="text-muted">Aucun artisan dans cette catégorie</h6>
                            <button class="btn btn-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#addArtisanModal" onclick="$('#type').selectpicker('val', '{{ type }}')">
                                <i class="bi bi-plus me-1"></i>Ajouter le premier artisan
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal d'ajout d'artisan -->
<div class="modal fade" id="addArtisanModal" tabindex="-1" aria-labelledby="addArtisanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 pb-0">
                <div>
                    <h5 class="modal-title fw-bold text-dark" id="addArtisanModalLabel">
                        <i class="bi bi-person-plus me-2 text-primary"></i>Nouvel Artisan
                    </h5>
                    <p class="text-muted mb-0 small">Ajouter un nouvel artisan à votre équipe</p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ path('add_artisan') }}" method="post">
                <div class="modal-body">
                    <div class="mb-4">
                        <label for="nom" class="form-label fw-semibold text-dark">
                            <i class="bi bi-person me-1"></i>Nom de l'artisan
                        </label>
                        <input type="text" class="form-control form-control-lg" id="nom" name="nom" required
                               placeholder="Entrez le nom de l'artisan">
                    </div>
                    <div class="mb-4">
                        <label for="type" class="form-label fw-semibold text-dark">
                            <i class="bi bi-tools me-1"></i>Corps de métier
                        </label>
                        <select class="selectpicker" id="type" name="type" required data-live-search="true"
                                data-style="form-control form-control-lg" data-width="100%" title="Sélectionner un métier">
                            {% for type, artisans in groupedArtisans %}
                                <option value="{{ type }}" data-content="<span class='badge me-2' style='background: {{ categories[type] }};color: {{ categories[type] }}; font-size: 0.7rem;'>●</span>
                                    {% if type == 'Maconnerie' %}
                                        Maçonnerie
                                    {% elseif type == 'PlomberieElec' %}
                                        Plomberie Électricité Chauffage
                                    {% elseif type == 'Etancheite' %}
                                        Étanchéité
                                    {% elseif type == 'LaineSouffle' %}
                                        Laine Soufflée
                                    {% elseif type == 'Permeabilite' %}
                                        Perméabilité
                                    {% else %}
                                        {{ type }}
                                    {% endif %}">
                                    {% if type == 'Maconnerie' %}
                                        Maçonnerie
                                    {% elseif type == 'PlomberieElec' %}
                                        Plomberie Électricité Chauffage
                                    {% elseif type == 'Etancheite' %}
                                        Étanchéité
                                    {% elseif type == 'LaineSouffle' %}
                                        Laine Soufflée
                                    {% elseif type == 'Permeabilite' %}
                                        Perméabilité
                                    {% else %}
                                        {{ type }}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="bi bi-plus-circle me-1"></i>Ajouter l'artisan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Fonction de recherche
    $('#searchArtisan').on('input', function() {
        let searchTerm = $(this).val().toLowerCase();
        $('.artisan-card').each(function() {
            let artisanName = $(this).find('.artisan-name').text().toLowerCase();
            let artisanType = $(this).find('small').text().toLowerCase();

            if (artisanName.includes(searchTerm) || artisanType.includes(searchTerm)) {
                $(this).closest('.col-lg-4').show();
            } else {
                $(this).closest('.col-lg-4').hide();
            }
        });

        // Masquer les sections vides
        $('.div-type').each(function() {
            let visibleCards = $(this).find('.artisan-card:visible').length;
            if (visibleCards === 0 && searchTerm !== '') {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
    });

    // Filtre par type
    $('#filtre-type').on('change', function() {
        let type = $(this).val();
        if (type !== 'ALL') {
            $('.div-type').hide();
            $(`.div-type[type="${type}"]`).show();
        } else {
            $('.div-type').show();
        }
        // Réinitialiser la recherche
        $('#searchArtisan').val('');
        $('.artisan-card').closest('.col-lg-4').show();
    });

    // Mode édition
    $(document).on('click', '.edit-artisan', function(e) {
        e.preventDefault();
        let card = $(this).closest('.artisan-card');
        card.find('.artisan-display').hide();
        card.find('.artisan-edit').show();
        card.find('.artisan-input').focus();
    });

    // Annuler édition
    $(document).on('click', '.cancel-edit', function() {
        let card = $(this).closest('.artisan-card');
        let originalName = card.find('.artisan-name').text();
        card.find('.artisan-input').val(originalName);
        card.find('.artisan-display').show();
        card.find('.artisan-edit').hide();
    });

    // Sauvegarder modification
    $(document).on('click', '.save-artisan', function() {
        let card = $(this).closest('.artisan-card');
        let id = card.attr('id-artisan');
        let newName = card.find('.artisan-input').val().trim();

        if (newName === '') {
            Toast.fire({
                icon: 'error',
                title: 'Le nom ne peut pas être vide'
            });
            return;
        }

        let url = "{{ path('edit_artisan', {'id': '0'}) }}".replace('0', id);

        $.ajax({
            url: url,
            type: 'PUT',
            data: { nom: newName },
            success: function(response) {
                Toast.fire({
                    icon: 'success',
                    title: 'Artisan modifié avec succès'
                });
                card.find('.artisan-name').text(newName);
                card.find('.artisan-display').show();
                card.find('.artisan-edit').hide();
            },
            error: function() {
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors de la modification'
                });
            }
        });
    });

    // Validation avec Entrée
    $(document).on('keypress', '.artisan-input', function(e) {
        if (e.which === 13) { // Entrée
            $(this).closest('.artisan-card').find('.save-artisan').click();
        } else if (e.which === 27) { // Échap
            $(this).closest('.artisan-card').find('.cancel-edit').click();
        }
    });

    // Suppression d'artisan
    $(document).on('click', '.delete-artisan', function(e) {
        e.preventDefault();
        let card = $(this).closest('.artisan-card');
        let id = card.attr('id-artisan');
        let artisanName = card.find('.artisan-name').text();

        Swal.fire({
            title: 'Supprimer cet artisan ?',
            html: `Êtes-vous sûr de vouloir supprimer <strong>${artisanName}</strong> ?<br><small class="text-muted">Cette action est irréversible.</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-trash me-1"></i>Oui, supprimer',
            cancelButtonText: '<i class="bi bi-x me-1"></i>Annuler',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                let url = "{{ path('delete_artisan', {'id': '0'}) }}".replace('0', id);

                $.ajax({
                    url: url,
                    type: 'DELETE',
                    success: function(response) {
                        // Animation de suppression
                        card.closest('.col-lg-4').fadeOut(300, function() {
                            $(this).remove();

                            // Vérifier si la section est vide
                            let section = card.closest('.div-type');
                            if (section.find('.artisan-card').length === 0) {
                                section.find('.card-body .row').html(`
                                    <div class="col-12 text-center py-4">
                                        <i class="bi bi-person-plus display-4 text-muted mb-3"></i>
                                        <h6 class="text-muted">Aucun artisan dans cette catégorie</h6>
                                        <button class="btn btn-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#addArtisanModal">
                                            <i class="bi bi-plus me-1"></i>Ajouter le premier artisan
                                        </button>
                                    </div>
                                `);
                            }
                        });

                        Toast.fire({
                            icon: 'success',
                            title: 'Artisan supprimé avec succès'
                        });
                    },
                    error: function() {
                        Toast.fire({
                            icon: 'error',
                            title: 'Erreur lors de la suppression'
                        });
                    }
                });
            }
        });
    });

    // Réinitialiser le modal à la fermeture
    $('#addArtisanModal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        $('#type').selectpicker('refresh');
    });
});
</script>

<style>
/* Styles pour la page des artisans */
.artisan-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef !important;
    transition: all 0.3s ease;
    cursor: default;
}

.artisan-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    border-color: #dee2e6 !important;
}

/* Styles pour la barre de recherche */
#searchArtisan {
    border-radius: 8px;
    transition: all 0.3s ease;
}

#searchArtisan:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    background-color: #f8f9fa !important;
    border-color: #dee2e6;
}

/* Styles pour les sections */
.card-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.artisan-card {
    animation: fadeInUp 0.5s ease-out;
}

/* Styles pour les dropdowns */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Styles pour le modal */
.modal-content {
    border-radius: 12px;
}

.modal-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px 12px 0 0;
}

/* Responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 15px;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }

    .input-group {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .artisan-card {
        margin-bottom: 1rem;
    }

    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
}
</style>
{% endblock %}
