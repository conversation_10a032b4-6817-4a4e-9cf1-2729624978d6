<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>{% block title %}{% endblock %}</title>
        
        <link rel="icon" type="image/x-icon" href="logo_petit_bbc.png" />

        {% block stylesheets %}
            {{ encore_entry_link_tags('app') }}
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/css/bootstrap-select.min.css" integrity="sha512-g2SduJKxa4Lbn3GW+Q7rNz+pKP9AWMR++Ta8fgwsZRCUsawjPvF/BxSMkGS61VsR9yinGoEgrHPGPn2mrj8+4w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        {% endblock %}

        {% block javascripts %}
            {{ encore_entry_script_tags('app') }}
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>            
            <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta3/js/bootstrap-select.min.js" integrity="sha512-yrOmjPdp8qH8hgLfWpSFhC/+R9Cj9USL8uJxYIveJZGAiedxyIxwNw4RsLDlcjNlIRR4kkHaDHSmNHAkxFTmgg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <script src="https://cdn.lordicon.com/lordicon.js"></script>

        {% endblock %}
    </head>
    <style>

    /* Styles pour le modal des chantiers archivés */
    .archived-card {
        transition: all 0.3s ease;
        border-radius: 8px !important;
        overflow: hidden;
    }

    .archived-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .archived-card .card-body {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    }

    .archived-card .card-title {
        font-size: 1rem;
        line-height: 1.2;
    }

    .archived-card .btn {
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .archived-card .btn:hover {
        transform: translateY(-1px);
    }

    .archived-card .btn-outline-success:hover {
        background-color: #198754;
        border-color: #198754;
        color: white;
    }

    .archived-card .btn-outline-danger:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    /* Animation d'entrée pour les cards */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .archived-card {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Styles pour la barre de recherche */
    #searchArchived {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    #searchArchived:focus {
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        border-color: #86b7fe;
    }

    .input-group-text {
        border-radius: 8px 0 0 8px;
        background-color: #f8f9fa !important;
        border-color: #dee2e6;
    }

    /* Amélioration du modal */
    #archiveModal .modal-content {
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }

    #archiveModal .modal-body {
        max-height: 70vh;
        overflow-y: auto;
    }

    /* Scrollbar personnalisée */
    #archiveModal .modal-body::-webkit-scrollbar {
        width: 6px;
    }

    #archiveModal .modal-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #archiveModal .modal-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    #archiveModal .modal-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .archived-card .btn span {
            display: none !important;
        }

        .archived-card .btn {
            padding: 0.375rem 0.5rem;
        }

        #archiveModal .modal-dialog {
            margin: 0.5rem;
        }

        .archived-card {
            margin-bottom: 1rem;
        }
    }



    .ref-button-mof {
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 5px 10px;
        cursor: pointer;
        text-decoration: none;
    }

    .fw-500 {
        font-weight: 500!important;
    }

    * {
        font-family: sans-serif;
    }

    /* 
    body{
        background: url("bg-bbc.svg") center fixed;
        background-size: cover;
    } */
     
    /* Pour Chrome, Safari et Edge */
    ::-webkit-scrollbar-track
    {
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
        border-radius: 10px;
        background-color: #F5F5F5;
    }

    ::-webkit-scrollbar
    {
        width: 8px;
        height: 8px;
        background-color: #F5F5F5;
    }

    ::-webkit-scrollbar-thumb
    {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
        background-color: #555;
    }

    body{
        background-color: #fdfdfd;
    }

    </style>
    <body class="mb-3">
        {% block header %}
            {% include 'tools/_header.html.twig' %}
        {% endblock %}
        <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });
        {% for message in app.flashes('success') %}
            Toast.fire({
                icon: "success",
                title: "{{ message }}"
            });
        {% endfor %}
        {% for message in app.flashes('danger') %}
            Toast.fire({
                icon: "error",
                title: "{{ message }}"
            });
        {% endfor %}
        </script>
        {% block body %}        
        {% endblock %}

    </body>
</html>