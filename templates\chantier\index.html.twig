{% extends 'base.html.twig' %}
{% import 'tools/macroBadge.html.twig' as macros %}

{% block title %}Chantier - Général{% endblock %}

{% block body %}
<style>
table>*{
	white-space: nowrap;
}

.sticky-col {
	position: sticky;
	left: 0;
	z-index: 100;
	background: #007bff;
}

/* th following the scroll not if print */
th{
	position: sticky;
	top: 0;
	z-index: 99;
}

th.sticky-col {
	position: sticky;
	top: 0;
	z-index: 101;
}


.hover-nav-modal{
	transition: 0.3s;
}

.hover-nav-modal:hover{
	background-color: #e0e0e0;
}

.active.hover-nav-modal{
	background-color: #e0e0e0;
}

.artisan{
	cursor: pointer;
}

.badge{
	font-size: 0.85rem!important;
	transition: 0.3s;
}

#hide-show-done {
	transform: scale(2);
}

#hide-show-done:checked{
	background-color: #198754;
	border-color: #198754;
}

#hide-show-done:focus{
	box-shadow: none;
}

#table-container{
	overflow-x: auto!important;
	border: 1px solid #ccc!important;
	border-radius: 5px!important;
	background-color: white!important;
}

@media (min-width: 2000px) {
	#table-container {
		height: 85vh!important;
	}
}

@media (max-width: 2000px) {
	#table-container {
		height: 75vh!important;
	}
}

#actionbutton:hover{
	background-color: #e0e0e0!important;
	transition: 0.3s;
}

#actionbutton{
	transition: 0.3s;
}

.dropcustom{
	transition: 0.3s;
}


</style>
{% set cdt_colors = {
	'ROMUALD': '#56AB30',
	'Romuald': '#56AB30',
	'romuald': '#56AB30',
	'HUGO': '#372A80',
	'Hugo': '#372A80',
	'hugo': '#372A80',
	'SÉBASTIEN': '#0000FF',
	'Sébastien': '#0000FF',
	'sébastien': '#0000FF',
	'default': ['#3E3A6D', '#5FAF45', '#2E2A5A']
} %}

<div class="mx-5 mt-3">
	<div class="row mb-2 mt-0 mx-0" id="filter-container">
		<div class="col-11 col-xl-9 mx-0 g-2 mt-0 d-flex align-items-center gap-2 px-0">
			<div class="mt-0">
				<label for="hide-show-chantier" class="form-label w-100 mb-0">Chantiers</label>
				<select class="selectpicker" data-style="btn border" id="hide-show-chantier" title="Masquer/Montrer les chantiers" multiple data-width="220px" data-actions-box="true" data-live-search="true">
					{% for chantier in chantiers %}
						{% if chantier.archive == false %}
							{% set color = cdt_colors[chantier.cdt] is defined ? cdt_colors[chantier.cdt] : cdt_colors['default'][loop.index0 % cdt_colors['default']|length] %}
							<option selected value="{{ chantier.id }}" data-content="<span class='badge' style='background: {{ color }}; color: {{ color }};'>-</span> {{ chantier.client }}">{{ chantier.client }}</option>
						{% endif %}
					{% endfor %}
				</select>
			</div>
			<div class="mt-0">
				<label for="hide-show-metier" class="form-label w-100 mb-0">Métiers</label>
				<select class="selectpicker" data-style="btn border" id="hide-show-metier" title="Masquer/Montrer les métiers" multiple data-width="220px" data-actions-box="true" data-live-search="true">
					<option selected value="Terrassement" data-content="<span class='badge' style='background: #996633;color: #996633'>-</span> Terrassement">Terrassement</option>
					<option selected value="Maconnerie" data-content="<span class='badge' style='background: #C00000;color: #C00000'>-</span> Maçonnerie">Maçonnerie</option>
					<option selected value="PlomberieElec" data-content="<span class='badge' style='background: #00B0F0;color: #00B0F0'>-</span> Plomberie Élec">Plomberie Élec</option>
					<option selected value="Charpente" data-content="<span class='badge' style='background: #009900;color: #009900'>-</span> Charpente">Charpente</option>
					<option selected value="Etancheite" data-content="<span class='badge' style='background: #008000;color: #008000'>-</span> Étanchéité">Étanchéité</option>
					<option selected value="Zinguerie" data-content="<span class='badge' style='background: #8497B0;color: #8497B0'>-</span> Zinguerie">Zinguerie</option>
					<option selected value="Menuiserie" data-content="<span class='badge' style='background: #000000;color: #000000'>-</span> Menuiserie">Menuiserie</option>
					<option selected value="Placo" data-content="<span class='badge' style='background: #CC99FF;color: #CC99FF'>-</span> Placo">Placo</option>
					<option selected value="Chappe" data-content="<span class='badge' style='background: #00CC99;color: #00CC99'>-</span> Chappe">Chappe</option>
					<option selected value="EntretientPAC" data-content="<span class='badge' style='background: rgb(101, 61, 13);color: rgb(101, 61, 13)'>-</span> Entretient PAC">Entretient PAC</option>
					<option selected value="Enduit" data-content="<span class='badge' style='background: #FFC000;color: #FFC000'>-</span> Enduit">Enduit</option>
					<option selected value="LaineSouffle" data-content="<span class='badge' style='background: #A50021;color: #A50021'>-</span> Laine Soufflée">Laine Soufflée</option>
					<option selected value="Carrelage" data-content="<span class='badge' style='background: #ED7D31;color: #ED7D31'>-</span> Carrelage">Carrelage</option>
					<option selected value="Escalier" data-content="<span class='badge' style='background: #A5A5A5;color: #A5A5A5'>-</span> Escalier">Escalier</option>
					<option selected value="Finition" data-content="<span class='badge' style='background: #3F888F;color: #3F888F'>-</span> Finition">Finition</option>
					<option selected value="Peinture" data-content="<span class='badge' style='background: #E4C6C9;color: #E4C6C9'>-</span> Peinture">Peinture</option>
					<option selected value="Permeabilite" data-content="<span class='badge' style='background: #9966FF;color: #9966FF'>-</span> Perméabilité">Perméabilité</option>
				</select>
			</div>
			<div class="mt-0">
				<label for="hide-show-artisan" class="form-label w-100 mb-0">Artisans</label>
				<select class="selectpicker" data-style="btn border" id="hide-show-artisan" title="Masquer/Montrer les artisans" multiple data-width="220px" data-actions-box="true" data-live-search="true">
					{% for label, type in artisans_optgroup %}
						<optgroup label="{{ label }}">
							{% for artisan in type %}
								<option metier="{{ label }}" data-tokens="{{ artisan.nom }} {{ label }}" selected value="{{ artisan.id }}" >{{ artisan.nom }}</option>
							{% endfor %}
						</optgroup>
					{% endfor %}
				</select>
			</div>
			<div class="mt-0">
				<label for="hide-show-cdt" class="form-label w-100 mb-0">CDT</label>
				<select class="selectpicker" data-style="btn border" id="hide-show-cdt" title="Masquer/Montrer les CDT" multiple data-width="220px" data-actions-box="true" data-live-search="true">
					{% for cdt in cdts %}
						{% set color = cdt_colors[cdt.CDT] is defined ? cdt_colors[cdt.CDT] : cdt_colors['default'][loop.index0 % cdt_colors['default']|length] %}
						{% if app.user.username == '<EMAIL>' or app.user.username == '<EMAIL>' or app.user.username == '<EMAIL>' %}
							{% if ((cdt.CDT|lower == 'hugo') and app.user.username == '<EMAIL>') or ((cdt.CDT|lower == 'romuald') and app.user.username == '<EMAIL>') or ((cdt.CDT|lower == 'sébastien') and app.user.username == '<EMAIL>') %}
								<option selected value="{{ cdt.CDT }}" data-content="<span class='badge' style='background:{{ color }};color:{{ color }}'>-</span> {{ cdt.CDT }}">{{ cdt.CDT }}</option>
							{% else %}
								<option value="{{ cdt.CDT }}" data-content="<span class='badge' style='background:{{ color }};color:{{ color }}'>-</span> {{ cdt.CDT }}">{{ cdt.CDT }}</option>
							{% endif %}
						{% else %}
							<option selected value="{{ cdt.CDT }}" data-content="<span class='badge' style='background:{{ color }};color:{{ color }}'>-</span> {{ cdt.CDT }}">{{ cdt.CDT }}</option>
						{% endif %}
					{% endfor %}
				</select>
			</div>
			<div class="mt-0" id="hide-show-done-container" style="display: none;">
				<label for="hide-show-done" class="form-label w-100 mb-0">Terminés</label>
				<div class="d-flex gap-2 align-items-center">
					<input type="checkbox" class="form-check-input mt-0 ms-1" id="hide-show-done" checked>
					<select class="selectpicker hidden-selectpicker useless" data-style="invisible" data-width="fit" >
						<option selected value="done">.</option>
					</select>
				</div>
			</div>
		</div>
		<div class="dropdown col-1 d-xl-none d-flex gap-2 justify-content-end align-items-end px-0">
			<button class="btn border-0" type="button"  data-bs-toggle="dropdown" aria-expanded="false" id="actionbutton">
				<span class="d-none d-xl-inline">Actions</span> <i class="bi bi-three-dots"></i>
			</button>
			<ul class="dropdown-menu p-2 border-0 shadow dropcustom" aria-labelledby="actionbutton">
				<li class="rounded-3">
				<button id="archives" class="dropdown-item rounded-3" data-bs-toggle="modal" data-bs-target="#archiveModal">
					<i class="bi bi-archive me-2"></i>Archives
				</button>
				</li>
				<li class="rounded-3">
				<button class="print dropdown-item rounded-3">
					<i class="bi bi-printer me-2"></i>Imprimer
				</button>
				</li>
				{% if not is_granted('ROLE_WATCHER') %}
				<li class="rounded-3">
				<a href="{{ path('app_semaines_interdites') }}" class="dropdown-item rounded-3">
					<i class="bi bi-calendar-x me-2"></i>Gérer les semaines interdites
				</a>
				</li>
				<li class="rounded-3">
				<a href="{{ path('app_chantier_new') }}" class="dropdown-item rounded-3">
					<i class="bi bi-plus-circle me-2"></i>Créer un chantier
				</a>
				</li>
				{% endif %}
			</ul>
		</div>
		<div class="col-3 d-none d-xl-flex gap-2 justify-content-end align-items-end px-0">
			<button id="archives" class="btn btn-outline-dark" data-bs-toggle="modal" data-bs-target="#archiveModal"><i class="bi bi-archive"></i></button>
			<button class="print btn btn-secondary">Imprimer</button>
			<a href="{{ path('app_semaines_interdites') }}" class="btn btn-outline-warning" title="Gérer les semaines interdites">
				<i class="bi bi-calendar-x"></i>
			</a>
			{% if not is_granted('ROLE_WATCHER') %}
				<a href="{{ path('app_chantier_new') }}" class="btn btn-primary">Créer un chantier</a>
			{% endif %}
		</div>
	</div>
	<div id="table-container">
		<table id="table" class="table table-hover table-borderless mb-0" style="width:100%">
			<thead>
				<tr>
					<th style="background: #E5E5E5" class="sticky-col">Clients</th>
					<th style="background: #E5E5E5">Commune</th>
					<th style="background: #E5E5E5" class="text-center">CDT</th>
					<th style="background: #E5E5E5" class="text-center">Début du chantier</th>
					<th style="background: #996633" metier="Terrassement" class="text-white text-center align-middle">TERRASSIER</th>
					<th style="background: #C00000" metier="Maconnerie" class="text-white text-center align-middle">MAÇON</th>
					<th style="background: #C00000" metier="Maconnerie" class="text-white text-center align-middle">Fondation</th>
					<th style="background: #C00000" metier="Maconnerie" class="text-white text-center align-middle">Dalle</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">EPC</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Préparation dalle</th>
					<th style="background: #996633" metier="Terrassement" class="text-white text-center align-middle">Branchement</th>
					<th style="background: #C00000" metier="Maconnerie" class="text-white text-center align-middle">Fin élèvation</th>
					<th style="background: #C00000" metier="Maconnerie"  class="text-white text-center align-middle">Rampannage</th>
					<th style="background: #C00000" metier="Maconnerie"  class="text-white text-center align-middle">Relevés de seuils</th>
					<th style="background: #C00000" metier="Maconnerie"  class="text-white text-center align-middle">Seuils</th>
					<th style="background: #009900" metier="Charpente" class="text-white text-center align-middle">CHARPENTIER COUVREUR</th> 
					<th style="background: #009900" metier="Charpente" class="text-white text-center align-middle">Charpente</th>
					<th style="background: #009900" metier="Charpente" class="text-white text-center align-middle">Couverture</th>
					<th style="background: #008000" metier="Etancheite" class="text-white text-center align-middle">Étancheur</th>
					<th style="background: #008000" metier="Etancheite" class="text-white text-center align-middle">Intervention</th>
					<th style="background: #8497B0" metier="Zinguerie" class="text-white text-center align-middle">Artisan Zinguerie</th>
					<th style="background: #8497B0" metier="Zinguerie" class="text-white text-center align-middle">Couvertines</th>
					<th style="background: #8497B0" metier="Zinguerie" class="text-white text-center align-middle">Gouttière</th>
					<th style="background: #000000" metier="Menuiserie" class="text-white text-center align-middle">MENUISERIE</th>
					<th style="background: #000000" metier="Menuiserie" class="text-white text-center align-middle">Pose Menuiseries</th>
					<th style="background: #CC99FF" metier="Placo" class="text-white text-center align-middle">PLAQUISTE</th>
					<th style="background: #CC99FF" metier="Placo" class="text-white text-center align-middle">Préparation Placo</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Prépa Plomberie</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Prépa filerie</th>
					<th style="background: #CC99FF" metier="Placo" class="text-white text-center align-middle">Fin Placo</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Gros œuvre</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Finition électricité</th>
					<th style="background: #00CC99" metier="Chappe" class="text-white text-center align-middle">Chappe</th>
					<th style="background: #00CC99" metier="Chappe" class="text-white text-center align-middle">Chape liquide</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Pose PAC et BAC</th>
					<th style="background:rgb(101, 61, 13)" metier="EntretientPAC" class="text-white text-center align-middle">Entretient PAC</th>
					<th style="background:rgb(101, 61, 13)" metier="EntretientPAC" class="text-white text-center align-middle">MES</th>
					<th style="background: #FFC000" metier="Enduit" class="text-white text-center align-middle">Enduiseur</th>
					<th style="background: #FFC000" metier="Enduit" class="text-white text-center align-middle">Ravalement</th>
					<th style="background: #8497B0" metier="Zinguerie" class="text-white text-center align-middle">Pose descente</th>
					<th style="background: #A50021" metier="LaineSouffle" class="text-white text-center align-middle">ISOLATION</th>
					<th style="background: #A50021" metier="LaineSouffle" class="text-white text-center align-middle">ISOLATION Soufflée</th>
					<th style="background: #ED7D31" metier="Carrelage" class="text-white text-center align-middle">Carreleur</th>
					<th style="background: #ED7D31" metier="Carrelage" class="text-white text-center align-middle">Pose carrelage</th>
					<th style="background: #A5A5A5" metier="Escalier" class="text-white text-center align-middle">Fabricant Escalier</th>
					<th style="background: #A5A5A5" metier="Escalier" class="text-white text-center align-middle">Pose escalier</th>
					<th style="background: #CC99FF" metier="Placo" class="text-white text-center align-middle">Cloison après carrelage</th>
					<th style="background: #00B0F0" metier="PlomberieElec" class="text-white text-center align-middle">Finition Plomberie + Sanitaire</th>
					<th style="background: #3F888F" metier="Finition" class="text-white text-center align-middle">Finitions Intérieures</th>
					<th style="background: #ED7D31" metier="Carrelage" class="text-white text-center align-middle">Pose plinthe</th>
					<th style="background: #E4C6C9" metier="Peinture" class="text-white text-center align-middle">Peintre</th>
					<th style="background: #E4C6C9" metier="Peinture" class="text-white text-center align-middle">Peinture</th>
					<th style="background: #9966FF" metier="Permeabilite" class="text-white text-center align-middle">Perméa</th>
					<th style="background: #9966FF" metier="Permeabilite" class="text-white text-center align-middle">Perméa</th>
					<th style="background: #E5E5E5" class="text-center align-middle">Délai</th>
					<th style="background: #E5E5E5" class="text-center align-middle print-hide">Réception</th>
				</tr>
			</thead>
			<tbody>
				{% for chantier in chantiers %}
					{% if chantier.archive == false %}
					<tr chantier-id="{{ chantier.id }}" {% for metier, artisan in chantier.getAllArtisans() %} {{ metier }}="{{ artisan }}"{% endfor %} cdt="{{ chantier.cdt }}" >
						<td class="sticky-col fw-semibold show-modal">{{ chantier.client }}</td>
						<td class="show-modal" style="text-decoration: underline;">
							<i>{{ chantier.commune|capitalize  }}</i>
						</td>
						<td class="text-center show-modal">{{ chantier.cdt|capitalize  }}</td>
						
						{% if chantier.dateDeb is null or chantier.dateDeb == '' %}
							<td class="text-center align-middle show-modal"><span class="badge rounded-pill text-bg-secondary">Non défini</span></td>
						{% else %}
							<td class="text-center align-middle show-modal" date="{{ chantier.dateDeb|date('Ymd') }}">{{ chantier.dateDeb|date('d/m/Y') }}</td>
						{% endif %}
					
						<td metier="Terrassement" style="background: #99663333" class="text-center align-middle modal-form artisan">
						{% if chantier.Terrassement is not null %}
							{% if chantier.Terrassement.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Terrassement.artisan.id }}" >{{ chantier.Terrassement.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form artisan">
						{% if chantier.Maconnerie is not null %}
							{% if chantier.Maconnerie.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Maconnerie.artisan.id }}" >{{ chantier.Maconnerie.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Fondation', 'EtatFondation']) }}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Dalle', 'EtatDalle']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form artisan">
						{% if chantier.PlomberieElec is not null %}
							{% if chantier.PlomberieElec.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.PlomberieElec.artisan.id }}" >{{ chantier.PlomberieElec.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PrepaDalle', 'EtatPrepaDalle']) }}
						</td>
						<td metier="Terrassement" style="background: #99663333" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Branchement', 'EtatBranchement']) }}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Elevation', 'EtatElevation']) }}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Rampannage', 'EtatRampannage']) }}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['ReleveSeuil', 'EtatReleveSeuil']) }}
						</td>
						<td metier="Maconnerie" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Seuil', 'EtatSeuil']) }}
						</td>
						<td metier="Charpente" style="background: #00990033" class="text-center align-middle modal-form artisan">
						{% if chantier.Charpente is not null %}
							{% if chantier.Charpente.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Charpente.artisan.id }}" >{{ chantier.Charpente.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Charpente" style="background: #00990033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Charpentes', 'EtatCharpente']) }}
						</td>
						<td metier="Charpente" style="background: #00990033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Couverture', 'EtatCouverture']) }}
						</td>
						<td metier="Etancheite" style="background: #00990033" class="text-center align-middle modal-form artisan">
						{% if chantier.Etancheite is not null %}
							{% if chantier.Etancheite.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Etancheite.artisan.id }}" >{{ chantier.Etancheite.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Etancheite" style="background: #00800033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['EtancheiteIntervention', 'EtatEtancheiteIntervention']) }}
						</td>
						<td metier="Zinguerie" style="background: #8497B033" class="text-center align-middle modal-form artisan">
						{% if chantier.Zingurie is not null %}
							{% if chantier.Zingurie.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Zingurie.artisan.id }}" >{{ chantier.Zingurie.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Zinguerie" style="background: #8497B033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Couvertine', 'EtatCouvertine']) }}
						</td>
						<td metier="Zinguerie" style="background: #8497B033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Goutiere', 'EtatGoutiere']) }}
						</td>
						<td metier="Menuiserie" style="background: #00000033" class="text-center align-middle modal-form artisan">
						{% if chantier.Menuiserie is not null %}
							{% if chantier.Menuiserie.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Menuiserie.artisan.id }}" >{{ chantier.Menuiserie.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Menuiserie" style="background: #00000033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PoseMenuiserie', 'EtatPoseMenuiserie']) }}
						</td>
						<td metier="Placo" style="background: #CC99FF33" class="text-center align-middle modal-form artisan">
						{% if chantier.Placo is not null %}
							{% if chantier.Placo.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Placo.artisan.id }}" >{{ chantier.Placo.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						<td metier="Placo" style="background: #CC99FF33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PrepaPlaco', 'EtatPrepaPlaco']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PrepaFileriePlomberie', 'EtatPrepaFileriePlomberie']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PrepaFileriePlomberie', 'EtatPrepaFileriePlomberie']) }}
						</td>
						<td metier="Placo" style="background: #CC99FF33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['FinPlaco', 'EtatFinPlaco']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['GrosOeuvre', 'EtatGrosOeuvre']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['FinitionElec', 'EtatFinitionElec']) }}
						</td>
						<td metier="Chappe" style="background: #00CC9933" class="text-center align-middle modal-form artisan">
						{% if chantier.Chappe is not null %}
							{% if chantier.Chappe.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Chappe.artisan.id }}" >{{ chantier.Chappe.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Chappe" style="background: #00CC9933" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['ChappeIntervention', 'EtatChappeIntervention']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PosePACChaudiere', 'EtatPosePACChaudiere']) }}
						</td>
						<td metier="EntretientPAC" style="background:rgb(101, 61, 13)33" class="text-center align-middle modal-form artisan">
						{% if chantier.EntretientPAC is not null %}
							{% if chantier.EntretientPAC.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.EntretientPAC.artisan.id }}" >{{ chantier.EntretientPAC.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="EntretientPAC" style="background:rgb(101, 61, 13)33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['MES', 'EtatMES']) }}
						</td>
						<td metier="Enduit" style="background: #FFC00033" class="text-center align-middle modal-form artisan">
						{% if chantier.Enduit is not null %}
							{% if chantier.Enduit.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Enduit.artisan.id }}" >{{ chantier.Enduit.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Enduit" style="background: #FFC00033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Ravalement', 'EtatRavalement']) }}
						</td>
						<td metier="Zinguerie" style="background: #8497B033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Descente', 'EtatDescente']) }}
						</td>
						<td metier="LaineSouffle" style="background: #********" class="text-center align-middle modal-form artisan">
						{% if chantier.LaineSouffle is not null %}
							{% if chantier.LaineSouffle.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.LaineSouffle.artisan.id }}" >{{ chantier.LaineSouffle.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="LaineSouffle" style="background: #********" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['LaineSouffleIntervention', 'EtatLaineSouffleIntervention']) }}
						</td>
						<td metier="Carrelage" style="background: #ED7D3133" class="text-center align-middle modal-form artisan">
						{% if chantier.Carrelage is not null %}
							{% if chantier.Carrelage.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Carrelage.artisan.id }}" >{{ chantier.Carrelage.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Carrelage" style="background: #ED7D3133" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PoseCarrelage', 'EtatPoseCarrelage']) }}
						</td>
						<td metier="Escalier" style="background: #A5A5A533" class="text-center align-middle modal-form artisan">
						{% if chantier.escalier is not null %}
							{% if chantier.escalier.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.escalier.artisan.id }}" >{{ chantier.escalier.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Escalier" style="background: #A5A5A533" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PoseEscalier', 'EtatPoseEscalier']) }}
						</td>
						<td metier="Placo" style="background: #CC99FF33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Cloison', 'EtatCloison']) }}
						</td>
						<td metier="PlomberieElec" style="background: #00B0F033" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['FinitionPlomberieSanitaire', 'EtatFinitionPlomberieSanitaire']) }}
						</td>
						<td metier="Finition" style="background: #3F888F33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['Finitions', 'EtatFinition']) }}
						</td>
						<td metier="Carrelage" style="background: #ED7D3133" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PosePlinthe', 'EtatPosePlinthe']) }}
						</td>
						<td metier="Peinture" style="background: #E4C6C933" class="text-center align-middle modal-form artisan">
						{% if chantier.Peinture is not null %}
							{% if chantier.Peinture.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Peinture.artisan.id }}" >{{ chantier.Peinture.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Peinture" style="background: #E4C6C933" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PeintureIntervention', 'EtatPeinture']) }}
						</td>
						<td metier="Permeabilite" style="background: #9966FF33" class="text-center align-middle modal-form artisan">
						{% if chantier.Permeabilite is not null %}
							{% if chantier.Permeabilite.artisan is not null %}
								<p class="mb-0" id-artisan="{{ chantier.Permeabilite.artisan.id }}" >{{ chantier.Permeabilite.artisan }}</p>
							{% else %}
								<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
							{% endif %}
						{% else %}
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>
						{% endif %}
						</td>
						<td metier="Permeabilite" style="background: #9966FF33" class="text-center align-middle modal-form">
						{{ macros.etatBadge(chantier, ['PermeabiliteIntervention', 'EtatPermeabiliteIntervention']) }}
						</td>

						{% if chantier.dateFin is not null %}
						<td class="text-center align-middle show-modal" date="{{ chantier.dateFin|date('Ymd') }}">
							{{ chantier.dateFin|date('d/m/Y') }}
						</td>
						{% else %}
						<td class="text-center align-middle show-modal">
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-calendar-x-fill"></i></span>
						</td>
						{% endif %}

						{% if chantier.dateReel is not null %}
						<td class="text-center align-middle show-modal print-hide" date="{{ chantier.dateReel|date('Ymd') }}">
							{{ chantier.dateReel|date('d/m/Y') }}
						</td>
						{% else %}
						<td class="text-center align-middle show-modal print-hide">
							<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-calendar-x-fill"></i></span>
						</td>
						{% endif %}
					</tr>
					{% endif %}
				{% endfor %}
			</tbody>
		</table>
	</div>
	<div class="text-end">
		<span id="countChantiers" class="badge bg-primary">Chantiers</span>
	</div>
</div>

<div class="modal fade" id="modal" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-centered modal-lg">
		<div class="modal-content rounded-4 border-0">
			<div class="modal-body row mx-0 p-0">
				<div id="chantierContent">
					<div class="pt-3 d-flex justify-content-between px-3">
						<h5 class="modal-title pb-2 text-body-tertiary fw-bold" id="modalLabel">Chantier</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<input type="hidden" id="chantierId">
					<form id="chantierForm" class="pt-3 px-2">
						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="client" class="form-label fw-semibold mb-0">Client</label>
							</div>
							<div class="col-7">
								<input type="text" class="form-control" id="client" name="client" required>
							</div>
						</div>

						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="commune" class="form-label fw-semibold mb-0">Commune</label>
							</div>
							<div class="col-7">
								<input type="text" class="form-control" id="commune" name="commune" required list="communeList">
								<datalist id="communeList">
								</datalist>
							</div>
						</div>

						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="cdt" class="form-label fw-semibold mb-0">Conducteur de Travaux</label>
							</div>
							<div class="col-7">
								<input type="text" class="form-control" id="cdt" name="cdt" required list="cdtList">
								<datalist id="cdtList">
									{% for cdt in cdts %}
										<option value="{{ cdt.CDT }}">
									{% endfor %}
								</datalist>
							</div>
						</div>

						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="dateDeb" class="form-label fw-semibold mb-0">Date de Début</label>
							</div>
							<div class="col-7">
								<input type="date" class="form-control" id="dateDeb" name="dateDeb" required>
							</div>
						</div>

						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="dateFin" class="form-label fw-semibold mb-0">Délais Contractuel</label>
							</div>
							<div class="col-7">
								<input type="date" class="form-control" id="dateFin" name="dateFin">
							</div>
						</div>

						<div class="row mx-0 mb-3 align-items-center">
							<div class="col-5">
								<label for="dateReel" class="form-label fw-semibold mb-0">Date Réception</label>
							</div>
							<div class="col-7">
								<input type="date" class="form-control" id="dateReel" name="dateReel">
							</div>
						</div>

					</form>
					<div class="pb-3 d-flex justify-content-between align-items-center px-3">
						<div>
							<button type="button" class="rounded-3 btn btn-sm btn-secondary" id="archiverChantier"><i class="bi bi-archive"></i> Archiver</button>
							<button type="button" class="rounded-3 btn btn-sm btn-warning" id="mouvements"><i class="bi bi-arrow-repeat"></i> Mouvements</button>
						</div>
						<button type="button" class="rounded-3 btn btn-sm btn-primary" id="saveChantier">Enregistrer</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

{# Modal après la requête ajax saveweek, il se remplira avec les différents steps renvoyés par la requête #}
<div class="modal fade" id="modalWeek" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content rounded-4 border-0 p-2" >
			<div class="modal-body row mx-0">
				<div id="mass-actions">
					<div class="d-flex justify-content-between">
						<h6 class="modal-title pb-2 text-body-tertiary fw-bold" id="modalLabel">Étapes</h6>
						<div class="d-flex gap-3">
							<h6 class="modal-title pb-2 text-body-tertiary fw-bold" id="old-all">Refuser</h6>
							<h6 class="modal-title pb-2 text-body-tertiary fw-bold" id="new-all">Accepter</h6>
						</div>
					</div>
				</div>
				<p style="display: none" id="step-chantierId"></p>
				<div id="steps-div">
				</div>
				<div class="d-flex justify-content-between mt-3">
					<button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Ignorer</button>
					<button type="button" class="btn btn-sm btn-primary" id="send-steps"><span id="number-steps"></span> Approuver</button>
				</div>
			</div>
		</div>
	</div>
</div>

{# modal list the archived chantiers #}
<div class="modal fade" id="archiveModal" tabindex="-1" aria-labelledby="modalLabel" aria-hidden="true" data-bs-backdrop="static">
	<div class="modal-dialog modal-dialog-centered modal-xl">
		<div class="modal-content rounded-4 border-0">
			<div class="modal-body p-4">
				<div id="archivedContent">
					<div class="d-flex justify-content-between align-items-center mb-4">
						<div>
							<h5 class="modal-title text-dark fw-bold mb-0" id="modalLabel">
								<i class="bi bi-archive me-2 text-muted"></i>Chantiers Archivés
							</h5>
							{% set archivedCount = 0 %}
							{% for chantier in chantiers %}
								{% if chantier.archive == true %}
									{% set archivedCount = archivedCount + 1 %}
								{% endif %}
							{% endfor %}
							<small class="text-muted">{{ archivedCount }} chantier{{ archivedCount > 1 ? 's' : '' }} archivé{{ archivedCount > 1 ? 's' : '' }}</small>
						</div>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>

					<!-- Barre de recherche -->
					<div class="mb-4">
						<div class="input-group">
							<span class="input-group-text bg-light border-end-0">
								<i class="bi bi-search text-muted"></i>
							</span>
							<input type="text" class="form-control border-start-0 bg-light" id="searchArchived" placeholder="Rechercher par client, commune ou CDT...">
						</div>
					</div>

					<div class="row g-3" id="archived-cards-container">
						{% for chantier in chantiers %}
							{% if chantier.archive == true %}
							<div class="col-lg-6 col-xl-4">
								<div class="card h-100 shadow-sm border-0 archived-card" chantier-id-archive="{{ chantier.id }}">
									<div class="card-body p-3 position-relative">
										<!-- Border left colorée selon le CDT -->
										{% set cdt_color = '#6c757d' %}
										{% if chantier.cdt|upper == 'ROMUALD' %}
											{% set cdt_color = '#56AB30' %}
										{% elseif chantier.cdt|upper == 'HUGO' %}
											{% set cdt_color = '#372A80' %}
										{% elseif chantier.cdt|upper == 'SÉBASTIEN' %}
											{% set cdt_color = '#0000FF' %}
										{% endif %}
										<div class="position-absolute top-0 start-0 h-100 rounded-start" style="width: 4px; background-color: {{ cdt_color }};"></div>

										<!-- Contenu principal -->
										<div class="ps-3">
											<div class="d-flex justify-content-between align-items-start mb-2">
												<h6 class="card-title fw-bold text-dark mb-1">{{ chantier.client }}</h6>
											</div>

											<div class="mb-3">
												<div class="d-flex align-items-center mb-1">
													<i class="bi bi-geo-alt-fill text-muted me-2" style="font-size: 0.8rem;"></i>
													<small class="text-muted">{{ chantier.commune|capitalize }}</small>
												</div>
												<div class="d-flex align-items-center mb-1">
													<i class="bi bi-person-fill text-muted me-2" style="font-size: 0.8rem;"></i>
													<small class="text-muted">{{ chantier.cdt }}</small>
												</div>
												<div class="d-flex align-items-center">
													<i class="bi bi-calendar-fill text-muted me-2" style="font-size: 0.8rem;"></i>
													<small class="text-muted">{{ chantier.dateDeb|date('d/m/Y') }} - {{ chantier.dateFin|date('d/m/Y') }} ({{ chantier.dateReel|date('d/m/Y') }})</small>
												</div>
											</div>

											<!-- Boutons d'action -->
											<div class="d-flex gap-2">
												<button type="button" chantier-id-archive="{{ chantier.id }}" class="btn btn-sm btn-outline-success unarchive flex-fill">
													<i class="bi bi-arrow-up-circle me-1"></i>
													<span class="d-none d-sm-inline">Désarchiver</span>
												</button>
												<button type="button" chantier-id-archive="{{ chantier.id }}" class="btn btn-sm btn-outline-danger delete flex-fill">
													<i class="bi bi-trash me-1"></i>
													<span class="d-none d-sm-inline">Supprimer</span>
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>
							{% endif %}
						{% endfor %}
					</div>

					<!-- Message si aucun chantier archivé -->
					{% set hasArchivedChantiers = false %}
					{% for chantier in chantiers %}
						{% if chantier.archive == true %}
							{% set hasArchivedChantiers = true %}
						{% endif %}
					{% endfor %}

					{% if not hasArchivedChantiers %}
					<div class="text-center py-5">
						<i class="bi bi-archive display-1 text-muted mb-3"></i>
						<h5 class="text-muted">Aucun chantier archivé</h5>
						<p class="text-muted mb-0">Les chantiers archivés apparaîtront ici</p>
					</div>
					{% endif %}
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.badge-input{
	font-size: 1em!important;
	border: none;
	width: 100%;
	height: 100%;
	-moz-appearance: textfield;
	padding: 0;
	text-align: center;
	background: transparent;
	font-weight: 700;
	font-family: "Arial", sans-serif;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

#cp{
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
}
</style>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script>

// document ready
$(document).ready(function() {
	if(window.location.hash){
		let hash = window.location.hash;
		let order = hash.split('-')[1];
		let column = hash.split('-')[2];
		let th = $('#table thead th').eq(column);
		th.addClass('sort-' + order);
		th.trigger('click');
	}
});

function reloadTable() {
	$.ajax({
		url: "{{ path('app_chantier') }}",
		type: 'GET',
		success: function(data) {
			let table = $(data).find('#table').html();
			let archivedCards = $(data).find('#archived-cards-container').html();
			$('#table').html(table);
			$('#archived-cards-container').html(archivedCards);
			hideShowTDTH();
			hideShowTR();
			if(window.location.hash){
				let hash = window.location.hash;
				let order = hash.split('-')[1];
				let column = hash.split('-')[2];
				let th = $('#table thead th').eq(column);
				th.addClass('sort-' + order);
				th.trigger('click');
			}
		},
		error: function() {
			console.log('Erreur lors du rechargement de la table');
		}
	});
}

async function getChantier(chantierId) {
	return new Promise((resolve, reject) => {
		$.ajax({
			url: "{{ path('app_chantier_getChantier') }}",
			type: 'POST',
			data: { chantierId },
			success: function(data) {
				resolve(data['chantier']);
			},
			error: function() {
				reject(null);
			}
		});
	});
}

function fillChantierForm(chantier) {
	$('#client').val(chantier.Client);
	$('#commune').val(chantier.Commune);
	$('#cdt').val(chantier.CDT);
	try {
		$('#dateDeb').val(chantier.DateDeb.date.split(' ')[0]);
	} catch (error) {
		$('#dateDeb').val('');
	}
	try {
		$('#dateFin').val(chantier.DateFin.date.split(' ')[0]);
	} catch (error) {
		$('#dateFin').val('');
	}
	try {
		$('#dateReel').val(chantier.dateReel.date.split(' ')[0]);
	} catch (error) {
		$('#dateReel').val('');
	}
	if(chantier.mouvements){
		$('#mouvements').attr('copy', chantier.mouvements);
		$('#mouvements').show();
	}else{
		$('#mouvements').hide();
	}
}

$(document).on('click', '#mouvements', function(){
	let copy = $(this).attr('copy');

	navigator.clipboard.writeText(copy).then(function() {
		Toast.fire({
			icon: 'success',
			title: 'Mouvements copiés dans le presse-papier'
		});
	}, function() {
		Toast.fire({
			icon: 'error',
			title: 'Erreur lors de la copie des mouvements'
		});
	});
});


async function showChantier() {
	let chantierId = $(this).closest('tr').attr('chantier-id');
	let chantier = await getChantier(chantierId);
	$('#chantierId').val(chantierId);
	$('#chantierForm').trigger('reset');
	$('.hover-nav-modal').removeClass('active');
	$('.chantier-nav').addClass('active');
	$('#metierContent').hide();
	$('#chantierContent').show();
	fillChantierForm(chantier);
	$('#modal').modal('show');
};

$('.chantier-nav').on('click', async function() {
	let chantierId = $('#chantierId').val();
	$('#chantierForm').trigger('reset');
	let chantier = await getChantier(chantierId);
	fillChantierForm(chantier);
	$('#metierContent').hide();
	$('#chantierContent').show();
	$('.hover-nav-modal').removeClass('active');
	$(this).addClass('active');
});

$('#saveChantier').on('click', function() {
	let chantierId = $('#chantierId').val();
	let client = $('#client').val();
	let commune = $('#commune').val();
	let cdt = $('#cdt').val();
	let dateDeb = $('#dateDeb').val();
	let dateFin = $('#dateFin').val();
	let dateReel = $('#dateReel').val();
	$.ajax({
		url: "{{ path('app_chantier_saveChantier') }}",
		type: 'POST',
		data: { chantierId, client, commune, cdt, dateDeb, dateFin, dateReel },
		success: function(data) {
			reloadTable();
			Toast.fire({
				icon: 'success',
				title: 'Modifications enregistrées !'
			});
		},
		error: function() {
		}
	});
});


const states = ['PAS FAIT', 'FAIT', 'NON'];
const statesClasses = ['text-bg-warning', 'text-bg-success', 'text-bg-dark'];
const icons = ['bi-hourglass-split', 'bi-check', 'bi-x'];

function change_etat(){
	var etat = $(this).attr('etat');
	var etape = $(this).attr('attr');
	var new_etat = states[(states.indexOf(etat) + 1) % states.length];
	var next_badge = $(this).next();

	if(new_etat == 'NON'){
		next_badge.text('Non');
	}else{
		if(!next_badge.attr('week')){
			next_badge.html('<i class="bi bi-pencil-fill"></i>');
		}else{
			next_badge.text('S' + next_badge.attr('week'));
		}
	}

	$(this).removeClass(statesClasses[states.indexOf(etat)]);
	$(this).addClass(statesClasses[states.indexOf(new_etat)]);
	next_badge.removeClass(statesClasses[states.indexOf(etat)]);
	next_badge.removeClass('p-0');
	next_badge.addClass(statesClasses[states.indexOf(new_etat)]);
	$(this).attr('etat', new_etat);
	$(this).find('i').removeClass("bi-pencil");
	$(this).find('i').removeClass(icons[states.indexOf(etat)]);
	$(this).find('i').addClass(icons[states.indexOf(new_etat)]);


	$.ajax({
		url: "{{ path('app_chantier_saveEtape') }}",
		type: 'POST',
		data: {
			chantierId: $(this).closest('tr').attr('chantier-id'),
			metier: $(this).closest('td').attr('metier'),
			etape: etape,
			etat: new_etat
		},
		success: function(data){
			Toast.fire({
				icon: 'success',
				title: 'Etat mis à jour'
			});
		},
		error: function() {
			Toast.fire({
				icon: 'error',
				title: 'Erreur lors de la mise à jour de l\'état'
			});
		}
	});

}

function input_week(){
	// Créer un input pour la semaine dans le badge remplir avec la semaine actuelle si elle existe sinon current week
	let badge = $(this);
	let etat_badge = $(this).prev();
	let etape = $(this).attr('attr');
	var week = $(this).attr('week');
	var input = $('<input type="number" class="form-control badge-input" min="1" max="52" value="' + (week ? week : '') + '">');
	// si il y a déja un input alors ne rien faire
	if(badge.find('input').length){
		return;
	}

	$(this).html(input);
	$(this).off('click');
	$(this).on('click', function(){
		$(this).find('input').focus();
	});
	$(this).find('input').focus();
	$(this).addClass('p-0');

	// Quand l'utilisateur appuie sur "Entrée", mettre à jour la semaine
	$(this).find('input').on('keypress blur', function(e){
		if(e.type === 'blur' || e.which == 13){
			var new_week = $(this).val();
			if((new_week < 1 || new_week > 52) && new_week != ''){
				Toast.fire({
					icon: 'error',
					title: 'La semaine doit être comprise entre 1 et 52'
				});
				return;
			}else{
				$('#step-chantierId').text($(this).closest('tr').attr('chantier-id'));
				$.ajax({
					url: "{{ path('app_chantier_saveWeek') }}",
					type: 'POST',
					data: {
						chantierId: $(this).closest('tr').attr('chantier-id'),
						metier: $(this).closest('td').attr('metier'),
						etape: etape,
						week: new_week
					},
					success: function(data){
						if(new_week){
							badge.attr('week', new_week);
							badge.text('S' + new_week);
							if(etat_badge.attr('etat') === 'NON'){
								etat_badge.click();
							}
							if (data.steps){
								$('#steps-div').html('');
								for (let step in data.steps) {
									if (data.steps.hasOwnProperty(step)) { // Vérifie que la propriété appartient bien à l'objet
										var new_steps = data.steps[step].new;
										var old = data.steps[step].old;
										var displayName = data.steps[step].displayName;
										if(!old){
											old = '∅';
										}
										// fill the #steps-box with the steps and radio buttons for each step the first one next to the old value and the second one next to the new value
										var stepDiv = $('<div class="d-flex justify-content-between align-items-center w-100"></div>');
										var step_name = $('<span>' + displayName + '</span>');
										var divValue = $('<div class="d-flex align-items-center"></div>');
										var oldStep = $('<div class="d-flex align-items-center"><span class="text-center" style="width: 2rem;">' + old + '</span><input type="radio" name="' + step + '" class="input-steps old-steps" value="' + old + '"></div>');
										var newStep = $('<div class="d-flex align-items-center"><input type="radio" name="' + step + '" class="input-steps new-steps" checked value="' + new_steps + '"><span class="text-center" style="width: 2rem;">' + new_steps + '</span></div>');
										divValue.append(oldStep);
										divValue.append($('<i class="bi bi-arrow-right mx-3"></i>'));
										divValue.append(newStep);
										stepDiv.append(step_name);
										stepDiv.append(divValue);
										$('#steps-div').append(stepDiv);
									}
								}
								$('#number-steps').html('Tout');
								$('#modalWeek').modal('show');
							}
						}else{
							badge.attr('week', '');
							badge.html('<i class="bi bi-pencil-fill"></i>');
							if(etat_badge.attr('etat') === 'NON'){
								badge.text('Non');
							}
						}
						badge.removeClass('p-0');
						badge.off('click');
						badge.on('click', input_week);
						Toast.fire({
							icon: 'success',
							title: 'Semaine mise à jour'
						});
					},
					error: function() {
						badge.on('click', input_week);
						Toast.fire({
							icon: 'error',
							title: 'Erreur lors de la mise à jour de la semaine'
						});
					}
				});
			}
		}
	});
}



let balise_hide_style = $('<style>').appendTo('head');
function hideShowTDTH(){
	var metiers = $('#hide-show-metier').val();
	$('td[metier]').each(function(){
		if(metiers.includes($(this).attr('metier'))){
			$(this).show();
		}else{
			$(this).hide();
		}
	});
	$('th[metier]').each(function(){
		if(metiers.includes($(this).attr('metier'))){
			$(this).show();
		}else{
			$(this).hide();
		}
	});

	var hidded_optgroup = [];
	$('#hide-show-artisan').find('optgroup').each(function(){
		var metier = $(this).attr('label');
		if(metiers.includes(metier)){
			$(this).attr('disabled', false);
		}else{
			$(this).attr('disabled', true);
			$(this).find('option').each(function(){
				$(this).prop('selected', true);
			});
			hidded_optgroup.push(($(this).index() + 1));
		}
	});
	$('#hide-show-artisan').selectpicker('destroy');
	$('#hide-show-artisan').selectpicker();
	setTimeout(() => {
		selectedAllArtisan();
		$('.bs-select-all')[2].innerText = 'Tout les artisans';
		$('.bs-deselect-all').text('Tout retirer');
		balise_hide_style.html('');
		hidded_optgroup.forEach(function(optgroup){
			balise_hide_style.append('.optgroup-'+optgroup+'{display: none!important;}');
			balise_hide_style.append('.optgroup-'+optgroup+'div{display: none!important;}');
		});
		hideShowTR();
	}, 10);
}

function hideShowTR(){
	var chantiers_id = $('#hide-show-chantier').val();
	var artisans_id = $('#hide-show-artisan').val();
	var disabled_id = $('#hide-show-artisan').find('optgroup[disabled]').find('option').map(function(){
		return $(this).val();
	}).get();
	var metiers = $('#hide-show-metier').val();
	if (metiers.length === 1) {
		$('#hide-show-done-container').show();
	}else{
		$('#hide-show-done-container').hide();
	}

	var cdts = $('#hide-show-cdt').val();
	$('tr[chantier-id]').each(function(){
		$(this).show();
		var showChantier = chantiers_id.includes($(this).attr('chantier-id'));
		var showCdt = cdts.includes($(this).attr('cdt'));
		var showArtisan = true;
		var ended = true;
		$(this).find('td[metier]').each(function(){
			var artisanId = $(this).find('p').attr('id-artisan');
			if (!artisans_id.includes(artisanId) && artisanId && !disabled_id.includes(artisanId)) {
				showArtisan = false;
			}
			if (metiers.length === 1 && $(this).is(':visible')) {
				if ($(this).find('.etat-badge').length && $(this).find('.etat-badge').attr('etat') !== 'FAIT' && $(this).find('.etat-badge').attr('etat') !== 'NON') {
					ended = false;
				}
			}
		});
		if (showChantier && showArtisan && showCdt) {
			$(this).show();
			if (!$("#hide-show-done").is(':checked') && ended && metiers.length === 1) {
				$(this).hide();
			}
		} else {
			$(this).hide();
		}
	});

	$('#countChantiers').text('Chantiers ' + $('tr[chantier-id]:visible').length);

}

$(document).on('change', '#hide-show-chantier', hideShowTR);
$(document).on('change', '#hide-show-metier', hideShowTDTH);
$(document).on('change', '#hide-show-artisan', hideShowTR);
$(document).on('change', '#hide-show-cdt', hideShowTR);
$(document).on('change', '#hide-show-done', hideShowTR);
hideShowTDTH();
hideShowTR();

function selectedAll() {
    let allOptions = $('#hide-show-metier').find('option').length;
    let selectedOptions = $('#hide-show-metier').val().length;

    if (selectedOptions === allOptions) {
        $('#hide-show-metier').next().find('.filter-option-inner-inner').text('Général');
    }
};

function selectedAllChantier() {
	let allOptions = $('#hide-show-chantier').find('option').length;
	let selectedOptions = $('#hide-show-chantier').val().length;

	if (selectedOptions === allOptions) {
		$('#hide-show-chantier').next().find('.filter-option-inner-inner').text('Tout les chantiers');

	}
};

function selectedAllArtisan() {
	if ($('#hide-show-artisan').find('optgroup[disabled]').length) {
		let selectedOptions = $('#hide-show-artisan').find('optgroup:not([disabled]) option:selected').map(function(){
			return $(this).text();
		}).get();
		$('#hide-show-artisan').next().find('.filter-option-inner-inner').text(selectedOptions.join(', '));
	}else{
		let allOptions = $('#hide-show-artisan').find('option').length;
		let selectedOptions = $('#hide-show-artisan').val().length;
		if (selectedOptions === allOptions) {
			$('#hide-show-artisan').next().find('.filter-option-inner-inner').text('Tout les artisans');
		}
	}	
};

function selectedAllCdt() {
	let allOptions = $('#hide-show-cdt').find('option').length;
	let selectedOptions = $('#hide-show-cdt').val().length;

	if (selectedOptions === allOptions) {
		$('#hide-show-cdt').next().find('.filter-option-inner-inner').text('Tout les CDT');
	}
};

setTimeout(() => {
	selectedAll();
	selectedAllChantier();
	selectedAllArtisan();
	selectedAllCdt();
	$('.bs-select-all')[1].innerText = 'Général';
	$('.bs-select-all')[0].innerText = 'Tout les chantiers';
	$('.bs-select-all')[2].innerText = 'Tout les artisans';
	$('.bs-select-all')[3].innerText = 'Tout les CDT';
	$('.bs-deselect-all').text('Tout retirer');
}, 10);
$(document).on('changed.bs.select', '#hide-show-metier', selectedAll);
$(document).on('changed.bs.select', '#hide-show-chantier', selectedAllChantier);
$(document).on('changed.bs.select', '#hide-show-artisan', selectedAllArtisan);
$(document).on('changed.bs.select', '#hide-show-cdt', selectedAllCdt);


function sortTable(){
	var table = $(this).closest('table');
	var th = $(this);
	var index = th.index();
	var tbody = table.find('tbody');
	var tr = tbody.find('tr').toArray().sort(function(a, b){
		var aText = $(a).find('td').eq(index).text();
		var bText = $(b).find('td').eq(index).text();

		if ($(a).find('td').eq(index).attr('date')) {
			aText = $(a).find('td').eq(index).attr('date');
		}
		if ($(b).find('td').eq(index).attr('date')) {
			bText = $(b).find('td').eq(index).attr('date');
		}

		if ($(a).find('td').eq(index).find('.badge[week]').length) {
			aText = $(a).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
		}
		if ($(b).find('td').eq(index).find('.badge[week]').length) {
			bText = $(b).find('td').eq(index).find('.badge[week]').attr('week').padStart(2, '0');
		}
		return aText.localeCompare(bText);
	});
	if(th.hasClass('sort-asc')){
		th.removeClass('sort-asc');
		th.addClass('sort-desc');
		tr = tr.reverse();
		$('th').find('i').remove();
		th.append('<i class="bi bi-sort-down"></i>');
		window.location.hash = 'sort-asc-' + index;
	}else{
		th.removeClass('sort-desc');
		th.addClass('sort-asc');
		$('th').find('i').remove();
		th.append('<i class="bi bi-sort-down-alt"></i>');
		window.location.hash = 'sort-desc-' + index;
	}
	tbody.empty();
	tbody.append(tr);
}

$(document).on('click', 'th', sortTable);

function artisantsSelectpicker(td){
	if(td.find('select').length){
		return;
	}
	var metier = td.attr('metier');
	var artisan = td.find('p').text();
	var artisanId = td.find('p').attr('id-artisan');
	$.ajax({
		url: "{{ path('get_artisans') }}",
		type: 'POST',
		data: { metier: metier },
		success: function(data){
			var div = $('<div class="d-flex justify-content-center align-items-center"></div>');
			var select = $('<select class="selectpicker" data-live-search="true" data-width="fit" data-style="btn-sm border bg-white" data-size="5" title="Artisan"></select>');
			select.append('<option value="" ></option>');
			data.forEach(function(art){
				if(art.id == artisanId){
					select.append('<option value="'+art.id+'" selected>'+art.Nom+'</option>');
				}else{
					select.append('<option value="'+art.id+'">'+art.Nom+'</option>');
				}
			});
			div.append(select);
			var validate = $('<button class="btn btn-primary btn-sm"><i class="bi bi-check"></i></button>');
			var cancel = $('<button class="btn btn-danger btn-sm"><i class="bi bi-x"></i></button>');
			div.append(validate);
			div.append(cancel);
			td.html(div);
			td.find('select').selectpicker();
			td.find('select').selectpicker('val', artisanId);
			validate.on('click', function(){				
				var artisanId = td.find('select').val();
				if (!artisanId) {
					artisanId = null;
				}
				$.ajax({
					url: "{{ path('app_chantier_setArtisan') }}",
					type: 'POST',
					data: {
						chantierId: td.closest('tr').attr('chantier-id'),
						metier: metier,
						artisanId: artisanId
					},
					success: function(data){
						Toast.fire({
							icon: 'success',
							title: 'Artisan mis à jour'
						});
						if(artisanId){
							td.html('<p class="mb-0" id-artisan="'+artisanId+'">'+td.find('select option:selected').text()+'</p>');
						}else{
							td.html('<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>');
						}
					},
					error: function(){
						Toast.fire({
							icon: 'error',
							title: 'Erreur lors de la mise à jour de l\'artisan'
						});
					}
				});
			});

			cancel.on('click', function(){
				if (artisanId) {
					td.html('<p class="mb-0" id-artisan="'+artisanId+'">'+artisan+'</p>');
				} else {
					td.html('<span class="badge rounded-pill text-bg-secondary"><i class="bi bi-person-x-fill"></i></span>');
				}
			});
		},
		error: function(){
			Toast.fire({
				icon: 'error',
				title: 'Erreur lors de la récupération des artisans'
			});
		}
	});
}

$('#old-all').on('click',function(){
	$('.old-steps').prop('checked', true);
	$('.new-steps').prop('checked', false);
});

$('#new-all').on('click',function(){
	$('.old-steps').prop('checked', false);
	$('.new-steps').prop('checked', true);
});


$(document).on('click', '.input-steps, #new-all, #old-all', function(){
    let checkedCount = $('.new-steps:checked').length;
    let totalCount = $('.new-steps').length;

    if (checkedCount === totalCount) {
        $('#number-steps').html('Tout');
    } else if(checkedCount === 0) {
		$('#number-steps').html('Aucun');
	}else {
		$('#number-steps').html(checkedCount);
    }
});

function send_steps(){
	var chantier = $('#step-chantierId').text();
	var steps_update = $('.new-steps:checked').map(function(){
		return {
			step: $(this).attr('name'),
			value: $(this).val()
		};
	}).get();
	if(steps_update.length === 0){
		$('#modalWeek').modal('hide');
		return;
	}
	
	$.ajax({
		url: "{{ path('app_chantier_saveWeekArray') }}",
		type: 'POST',
		data: {
			chantierId: chantier,
			steps: steps_update
		},
		success: function(data){
			Toast.fire({
				icon: 'success',
				title: 'Semaines mises à jour'
			});
			$('#modalWeek').modal('hide');
			reloadTable();
		},
		error: function(){
			Toast.fire({
				icon: 'error',
				title: 'Erreur lors de la mise à jour des semaines'
			});
		}
	});
}




$('.print').on('click', function(){
    Toast.fire({
        icon: 'info',
        title: 'Génération du PDF…',
    });
	redirect_printing();
});

function redirect_printing() {
	var metiers = $('#hide-show-metier').val();
	var chantiers = $('#hide-show-chantier').val();
	var artisans = $('#hide-show-artisan').val();
	var cdts = $('#hide-show-cdt').val();
	var hide_done = $('#hide-show-done').is(':checked');
	var url = "{{ path('app_chantier_print') }}?metiers=" + metiers.join(',') + "&chantiers=" + chantiers.join(',') + "&artisans=" + artisans.join(',') + "&cdts=" + cdts.join(',') + "&hide_done=" + hide_done;
	
	// open in new tab but don't show it
	window.open(url, '_blank', 'noopener,noreferrer');
}


// Fonction pour archiver un chantier
function archiverChantier(){
	var chantierId = $('#chantierId').val();
	Swal.fire({
		title: 'Êtes-vous sûr de vouloir archiver ce chantier ?',
		icon: 'warning',
		showCancelButton: true,
		confirmButtonText: 'Oui, archiver !',
		cancelButtonText: 'Annuler',
		customClass: {
			confirmButton: 'btn btn-danger',
			cancelButton: 'btn btn-secondary'
		}
	}).then((result) => {
		if (result.isConfirmed) {
			$.ajax({
				url: "{{ path('app_chantier_archiverChantier') }}",
				type: 'POST',
				data: { chantierId },
				success: function(data){
					reloadTable();
					$('#modal').modal('hide');
					Toast.fire({
						icon: 'success',
						title: 'Chantier archivé'
					});
				},
				error: function(){
					Toast.fire({
						icon: 'error',
						title: 'Erreur lors de l\'archivage du chantier'
					});
				}
			});
		}
	});
}

function unarchiveChantier(){
	var chantierId = $(this).attr('chantier-id-archive');
	Swal.fire({
		title: 'Êtes-vous sûr de vouloir désarchiver ce chantier ?',
		icon: 'warning',
		showCancelButton: true,
		confirmButtonText: 'Oui, désarchiver !',
		cancelButtonText: 'Annuler',
		customClass: {
			confirmButton: 'btn btn-danger',
			cancelButton: 'btn btn-secondary'
		}
	}).then((result) => {
		if (result.isConfirmed) {
			$.ajax({
				url: "{{ path('app_chantier_unarchiveChantier') }}",
				type: 'POST',
				data: { chantierId },
				success: function(data){
					$('#archiveModal').modal('hide');
					reloadTable();
					Toast.fire({
						icon: 'success',
						title: 'Chantier désarchivé'
					});
				},
				error: function(){
					Toast.fire({
						icon: 'error',
						title: 'Erreur lors du désarchivage du chantier'
					});
				}
			});
		}
	});
}

$(document).on('click', '.delete', function(){
	var chantierId = $(this).attr('chantier-id-archive');
	Swal.fire({
		title: 'Êtes-vous sûr de vouloir supprimer ce chantier ?',
		icon: 'warning',
		showCancelButton: true,
		confirmButtonText: 'Oui, supprimer !',
		cancelButtonText: 'Annuler',
		customClass: {
			confirmButton: 'btn btn-danger',
			cancelButton: 'btn btn-secondary'
		}
	}).then((result) => {
		if (result.isConfirmed) {
			$.ajax({
				url: "{{ path('app_chantier_deleteChantier') }}",
				type: 'POST',
				data: { chantierId },
				success: function(data){
					$('#archiveModal').modal('hide');
					reloadTable();
					Toast.fire({
						icon: 'success',
						title: 'Chantier supprimé'
					});
				},
				error: function(){
					Toast.fire({
						icon: 'error',
						title: 'Erreur lors de la suppression du chantier'
					});
				}
			});
		}
	});
});

// Fonction de recherche pour les chantiers archivés
$('#searchArchived').on('input', function() {
	let searchTerm = $(this).val().toLowerCase();
	$('.archived-card').each(function() {
		let card = $(this);
		let client = card.find('.card-title').text().toLowerCase();
		let commune = card.find('.bi-geo-alt-fill').next().text().toLowerCase();
		let cdt = card.find('.bi-person-fill').next().text().toLowerCase();

		if (client.includes(searchTerm) || commune.includes(searchTerm) || cdt.includes(searchTerm)) {
			card.closest('.col-lg-6').show();
		} else {
			card.closest('.col-lg-6').hide();
		}
	});

	// Afficher un message si aucun résultat
	let visibleCards = $('.archived-card:visible').length;
	if (visibleCards === 0 && searchTerm !== '') {
		if ($('#no-results-message').length === 0) {
			$('#archived-cards-container').append(`
				<div class="col-12 text-center py-4" id="no-results-message">
					<i class="bi bi-search display-4 text-muted mb-3"></i>
					<h6 class="text-muted">Aucun résultat trouvé</h6>
					<p class="text-muted mb-0">Essayez avec d'autres mots-clés</p>
				</div>
			`);
		}
	} else {
		$('#no-results-message').remove();
	}
});


{% if not is_granted('ROLE_WATCHER') %}
$(document).on('click', '.show-modal', showChantier);
$(document).on('click', '.unarchive', unarchiveChantier);
$(document).on('click', '#archiverChantier', archiverChantier);
$(document).on('click', '#send-steps', send_steps);
$(document).on('click', '.edit-badge', input_week);
$(document).on('click', '.etat-badge', change_etat);
$(document).on('click', '.artisan', function(){
	artisantsSelectpicker($(this));
});
{% endif %}
</script>

{% endblock %}
