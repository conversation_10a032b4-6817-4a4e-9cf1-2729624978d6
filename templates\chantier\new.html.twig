{% extends 'base.html.twig' %}
{% import 'tools/macroBadge.html.twig' as macros %}

{% block title %}Nouveau chantier{% endblock %}

{% block body %}
    <style>
    label{
        font-weight: 600!important;
    }

    /* chantier-card hover then border left appeard slowly on card body with color #56AB30 */
    #chantier-card:hover #chantier-card-body{
        border-left: 5px solid #56AB30;
        transition: 0.3s;
    }

    #chantier-card:hover{
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.30)!important;
        transition: 0.3s;
    }

    #chantier-card{
        transition: 0.3s;
    }

    #chantier-card-body{
        border-left: 5px solid transparent;
        border-radius:  0 0 0 5px!important;
        transition: 0.3s;
    }

    .focus-green:focus{
        box-shadow: 0 0 0 0.15rem rgba(86, 171, 48, 0.50)!important;
    }

    /* artisans-card hover then border left appeard slowly on card body with color #36277E */
    #artisans-card:hover #artisans-card-body{
        border-left: 5px solid #36277E;
        transition: 0.3s;
    }

    #artisans-card:hover{
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.30)!important;
        transition: 0.3s;
    }

    #artisans-card{
        transition: 0.3s;
    }
    
    #artisans-card-body{
        border-left: 5px solid transparent;
        border-radius:  0 0 0 5px!important;
        transition: 0.3s;
    }

    .focus-purple.show, .focus-purple:focus{
        box-shadow: 0 0 0 0.15rem rgba(54, 39, 126, 0.50)!important;
    }
    </style>
    <div style="margin: 0 5%" class="py-4">
        <div class="row mx-0">
            <!-- Colonne de gauche: Informations générales -->
            <div class="col-3 mb-4">
                <div class="card h-100 shadow border-0" id="chantier-card">
                    <div class="card-header text-white" style="background: #56AB30;">
                        <h5 class="mb-0 fw-bold">
                            <i class="me-2 fas fa-hard-hat"></i>
                            Chantier
                        </h5>
                    </div>
                    <div class="card-body" id="chantier-card-body">
                        {{ form_start(form, {'attr': {'class': 'needs-validation'}}) }}
                        <div class="row mx-0 g-3">
                            <div class="col-12">
                                {{ form_row(form.Client, {'attr': {'class': 'form-control focus-green rounded-3'}}) }}
                            </div>
                            <div class="col-12">
                                {{ form_row(form.Commune, {'attr': {'class': 'form-control focus-green rounded-3'}}) }}
                            </div>
                            <div class="col-12">
                                {{ form_row(form.CDT, {'attr': {'class': 'form-control focus-green rounded-3', 'list': 'cdtList'}}) }}
                                <datalist id="cdtList">
									{% for cdt in cdts %}
										<option value="{{ cdt.CDT }}">
									{% endfor %}
								</datalist>
                            </div>
                            <hr>
                            <div class="col-12 mt-0">
                                {{ form_row(form.DateDeb, {'attr': {'class': 'form-control focus-green rounded-3'}}) }}
                            </div>
                            <div class="col-5">
                                {{ form_row(form.delais, {'attr': {'class': 'form-control focus-green rounded-3'}}) }}
                            </div>
                            <div class="col-7">
                                {{ form_row(form.DateFin, {'attr': {'class': 'form-control focus-green rounded-3'}}) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Colonne de droite: Travaux à réaliser et actions -->
            <div class="col-9" >
                <div class="card mb-4 shadow border-0" id="artisans-card">
                    <div class="card-header text-white" style="background:  #36277E;">
                        <h5 class="mb-0 fw-bold">
                            <i class="me-2 fa-solid fa-hammer"></i>
                            Artisans
                        </h5>
                    </div>
                    <div class="card-body" id="artisans-card-body">
                        <div class="row mx-0 g-3">
                            {% for field in [
                                form.Terrassement, form.Chappe,
                                form.Maconnerie, form.Carrelage, 
                                form.Charpente, form.Enduit,
                                form.Etancheite, form.Finition,
                                form.Zingurie, form.EntretientPAC,
                                form.Menuiserie, form.Peinture,
                                form.Placo, form.Escalier,
                                form.LaineSouffle, form.Permeabilite,
                                form.PlomberieElec, 
                                
                            ] %}
                                <div class="col-md-6 row mx-0 g-2">
                                    <div class="col-5 d-flex align-items-center fw-semibold">
                                        {{ form_label(field) }}
                                    </div>
                                    <div class="col-7">{{ form_widget(field) }}</div>
                                </div>
                            {% endfor %}
                        </div>
                        <div class="d-flex justify-content-end align-items-center mt-3 gap-2">
                            <button type="submit" class="btn" style="background: #56AB30; color: white;">
                                Ajouter <i class="ms-2 fas fa-check"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {{ form_end(form) }}
            </div>
        </div>
    </div>


<script>
function dateDebCalc() {
    var dateDeb = $('#chantier_DateDeb').val();
    var delais = $('#chantier_delais').val();
    var dateFin = new Date(dateDeb);
    if (dateDeb && delais) {
        dateFin.setMonth(dateFin.getMonth() + parseInt(delais));
        $('#chantier_DateFin').val(dateFin.toISOString().slice(0, 10));
    }
};

$('#chantier_delais').change(dateDebCalc);
$('#chantier_DateDeb').change(dateDebCalc);

</script>
{% endblock %}
