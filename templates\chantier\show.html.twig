{% extends 'base.html.twig' %}
{% import 'tools/macroBadge.html.twig' as macros %}

{% block title %}Chantier - Détails{% endblock %}

{% block body %}

<div style="margin: 0 3%" class="mt-3">
    <div class="row gap-3">
        <div class="card col-4 px-0 border-0 shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title fw-bold mb-0">{{ chantier.client }}</h3>
                <h6 class="card-title fw-500 mb-0"><i class="fas fa-hard-hat"></i> {{ chantier.cdt }}</h6>
            </div>
            <div class="card-body d-flex justify-content-between align-items-center">
                <h5 class="card-title fw-500 mb-0"><i class="bi bi-house-fill"></i> {{ chantier.commune }}</h5>
                <h6 class="card-title fw-500 mb-0">
                    <i class="fa-regular fa-calendar-days"></i>
                    {% if chantier.dateDeb is not null %}{{ chantier.dateDeb|date('d/m/Y') }}{% endif %}
                    {% if chantier.dateFin is not null %} - {{ chantier.dateFin|date('d/m/Y') }}{% endif %}
                </h6>
            </div>
        </div>
        <div class="col">
        </div>
    </div>
</div>

{% endblock %}