{% extends 'base.html.twig' %}

{% block title %}Statistiques des Chantiers{% endblock %}

{% block body %}
<div class="container-fluid py-2">
    <div class="row justify-content-center">
        <div class="col-lg-11 col-xl-9">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h2 class="mb-0 fs-4"><i class="fa-solid fa-chart-line me-1"></i>Statistiques</h2>
                <div class="text-muted small">
                    <i class="fa-solid fa-calendar-day me-1"></i> {{ "now"|date("d/m/Y") }}
                </div>
            </div>
            <hr class="mb-3 mt-1">
        </div>
    </div>

    <!-- Statistiques globales -->
    <div class="row mb-3 justify-content-center">
        <div class="col-lg-11 col-xl-9">
            <div class="card shadow-sm">
                <div class="card-header bg-dark text-white py-1">
                    <h3 class="mb-0 fs-6"><i class="fa-solid fa-chart-pie me-1"></i>Statistiques Globales</h3>
                </div>
                <div class="card-body py-2">
                    <div class="row g-2">
                        <div class="col-md-3 text-center">
                            <div class="card border-success border-opacity-25 h-100">
                                <div class="card-body py-2 px-2 d-flex flex-column justify-content-center">
                                    <span class="card-title text-success small fw-bold mb-1">Chantiers en cours</span>
                                    <p class="fs-3 mb-0 fw-light">{{ globalStats.chantiersEnCours }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card border-info border-opacity-25 h-100">
                                <div class="card-body py-2 px-2 d-flex flex-column justify-content-center">
                                    <span class="card-title text-primary small fw-bold mb-1">Durée moyenne</span>
                                    <p class="fs-3 mb-0 fw-light">{{ globalStats.avgDuration }} <small class="text-muted">mois</small></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-center">
                            <div class="card border-warning border-opacity-25 h-100">
                                <div class="card-body py-2 px-2">
                                    <span class="card-title small fw-bold mb-1">Chantiers min/max</span>
                                    <div class="row">
                                        <div class="col-6 border-end">
                                            <p class="mb-0 text-muted small">Le plus rapide</p>
                                            {% if globalStats.fastestProject %}
                                                <p class="mb-0 small"><strong>{{ globalStats.fastestProject.client }}</strong></p>
                                                <p class="mb-0 small text-muted">{{ globalStats.fastestProject.commune }}</p>
                                                <p class="mb-0 text-success fw-bold">{{ globalStats.fastestProject.duration }} mois</p>
                                            {% else %}
                                                <p class="small">N/A</p>
                                            {% endif %}
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-0 text-muted small">Le plus long</p>
                                            {% if globalStats.longestProject %}
                                                <p class="mb-0 small"><strong>{{ globalStats.longestProject.client }}</strong></p>
                                                <p class="mb-0 small text-muted">{{ globalStats.longestProject.commune }}</p>
                                                <p class="mb-0 text-danger fw-bold">{{ globalStats.longestProject.duration }} mois</p>
                                            {% else %}
                                                <p class="small">N/A</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3 justify-content-center">
        <div class="col-lg-11 col-xl-9">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-secondary text-white py-1">
                            <h3 class="mb-0 fs-6"><i class="fa-solid fa-helmet-safety me-1"></i>Répartition par Conducteur (Chantiers en cours)</h3>
                        </div>
                        <div class="card-body py-2">
                            <div style="height: 280px;">
                                <canvas id="chartConducteurs"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-secondary text-white py-1">
                            <h3 class="mb-0 fs-6"><i class="fa-solid fa-map-location-dot me-1"></i>Répartition par Commune (Chantiers en cours)</h3>
                        </div>
                        <div class="card-body py-2">
                            <div style="height: 280px;">
                                <canvas id="chartCommunes"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3 justify-content-center">
        <div class="col-lg-11 col-xl-9">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white py-1">
                    <h3 class="mb-0 fs-6"><i class="fa-solid fa-calendar-day me-1"></i>Nombre de Chantiers par Année</h3>
                </div>
                <div class="card-body py-2">
                    <div style="height: 220px;">
                        <canvas id="chartChantiersByYear"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3 justify-content-center">
        <div class="col-lg-11 col-xl-9">
            <div class="card shadow-sm">
                <div class="card-header bg-dark text-white py-1">
                    <h3 class="mb-0 fs-6"><i class="fa-solid fa-calendar-week me-1"></i>Délais Moyens par Année et Conducteur</h3>
                </div>
                <div class="card-body py-2">
                    <div style="height: 300px;">
                        <canvas id="chartDelays"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Les canvases occuperont 100% de la largeur de leur conteneur */
    canvas {
        max-width: 100% !important;
        height: auto !important;
        filter: drop-shadow(0 3px 3px rgba(0,0,0,0.05));
    }

    /* Styles améliorés pour les cartes */
    .card {
        border-radius: 6px;
        overflow: hidden;
        transition: all 0.2s ease;
        border: none;
    }

    .card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.08) !important;
    }

    .card-header {
        border-bottom: none;
    }

    /* Animation pour les chiffres */
    .display-5 {
        animation: fadeIn 0.5s ease-out;
        font-weight: 500;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Amélioration du titre principal */
    h2.fs-3 {
        font-weight: 500;
        color: #333;
        border-left: 4px solid #6c757d;
        padding-left: 12px;
    }

    /* Ajustements pour les petits écrans */
    @media (max-width: 768px) {
        .container-fluid {
            padding: 10px;
        }

        .card-body {
            padding: 10px;
        }
    }

    /* Couleurs plus douces pour les bordures */
    .border-success {
        border-color: rgba(40, 167, 69, 0.5) !important;
    }

    .border-info {
        border-color: rgba(23, 162, 184, 0.5) !important;
    }

    .border-warning {
        border-color: rgba(255, 193, 7, 0.5) !important;
    }

    /* Amélioration de l'espacement général */
    .row {
        margin-left: -8px;
        margin-right: -8px;
    }

    .col-md-3, .col-md-4, .col-md-6, .col-md-12 {
        padding-left: 8px;
        padding-right: 8px;
    }
</style>

{# Inclusion de Chart.js depuis un CDN - version spécifique pour éviter les problèmes de compatibilité #}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    // Configuration globale de Chart.js pour des animations plus fluides
    try {
        // Vérifier si nous utilisons Chart.js v3+
        if (Chart.defaults.animation !== undefined) {
            Chart.defaults.animation = {
                duration: 1500,
                easing: 'easeOutQuart'
            };

            if (Chart.defaults.plugins && Chart.defaults.plugins.tooltip) {
                Chart.defaults.plugins.tooltip.padding = 10;
                Chart.defaults.plugins.tooltip.cornerRadius = 6;
                Chart.defaults.plugins.tooltip.titleFont = {
                    size: 14,
                    weight: 'bold'
                };
            }
        }
        // Compatibilité avec Chart.js v2
        else if (Chart.defaults.global) {
            Chart.defaults.global.animation = {
                duration: 1500,
                easing: 'easeOutQuart'
            };

            if (Chart.defaults.global.tooltips) {
                Chart.defaults.global.tooltips.cornerRadius = 6;
                Chart.defaults.global.tooltips.titleFontSize = 14;
                Chart.defaults.global.tooltips.titleFontStyle = 'bold';
                Chart.defaults.global.tooltips.xPadding = 10;
                Chart.defaults.global.tooltips.yPadding = 10;
            }
        }
    } catch (e) {
        console.error('Erreur lors de la configuration de Chart.js:', e);
    }

    // Palette de couleurs commune pour tous les graphiques
    const palette = [
        "#56AB30", "#372A80", "#0000FF", "#FF5733", "#C70039", "#900C3F",
        "#e6194B", "#3cb44b", "#ffe119", "#0082c8", "#f58231", "#911eb4", "#46f0f0", "#f032e6",
        "#d2f53c", "#fabebe", "#008080", "#e6beff", "#aa6e28", "#fffac8", "#800000", "#aaffc3",
        "#808000", "#ffd8b1", "#000080", "#808080", "#B22222", "#FF7F50",
        "#FFD700", "#008000", "#00FA9A", "#00CED1", "#1E90FF", "#C71585", "#696969", "#B8860B",
        "#006400", "#8B0000", "#FF4500", "#2E8B57", "#DAA520", "#9370DB", "#3CB371", "#7B68EE"
    ];

    // Couleurs fixes pour certains conducteurs
    const fixedColors = {
        "Hugo": "#56AB30",
        "Romuald": "#372A80",
        "Sébastien": "#0000FF"
    };

    // Couleurs de repli pour les autres conducteurs
    const fallbackColors = ['#FF5733', '#C70039', '#900C3F'];

    // Graphique : Répartition par Conducteur
    const conducteursLabels = {{ conducteurs|column('conducteur')|json_encode|raw }};
    const conducteursData = {{ conducteurs|column('total')|json_encode|raw }};

    new Chart(document.getElementById('chartConducteurs'), {
        type: 'doughnut',
        data: {
            labels: conducteursLabels,
            datasets: [{
                label: 'Nombre de chantiers',
                data: conducteursData,
                backgroundColor: palette.slice(0, conducteursLabels.length)
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                },
                legend: {
                    position: 'left'
                }
            }
        }
    });

    // Graphique : Répartition par Commune
    const communesLabels = {{ communes|column('label')|json_encode|raw }};
    const communesData = {{ communes|column('total')|json_encode|raw }};

    new Chart(document.getElementById('chartCommunes'), {
        type: 'doughnut',
        data: {
            labels: communesLabels,
            datasets: [{
                label: 'Nombre de chantiers',
                data: communesData,
                backgroundColor: palette.slice(0, communesLabels.length) // Utilisation de la palette pour les couleurs
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                },
                legend: {
                    position: 'right'
                }
            }
        }
    });

    // Graphique : Nombre de chantiers par année
    const chantiersByYearData = {{ chantiersByYear|json_encode|raw }};
    const yearLabels = chantiersByYearData.map(item => item.annee);
    const yearData = chantiersByYearData.map(item => item.total);

    new Chart(document.getElementById('chartChantiersByYear'), {
        type: 'bar',
        data: {
            labels: yearLabels,
            datasets: [{
                label: 'Nombre de chantiers',
                data: yearData,
                backgroundColor: '#ffc107',
                borderColor: '#ffc107',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Nombre de chantiers'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Année'
                    }
                }
            }
        }
    });



    // Récupération des données depuis le contrôleur (un tableau d'objets avec annee, conducteur et avgReel)
    const delaysData = {{ delaysByYear|json_encode|raw }};

    // Extraction et tri des années uniques
    const years = [...new Set(delaysData.map(item => item.annee))].sort();

    // Extraction des conducteurs uniques
    const conducteurs = [...new Set(delaysData.map(item => item.conducteur))];

    // Préparation des datasets : pour chaque conducteur, on construit un tableau contenant les moyennes (avgReel) pour chaque année
    const datasets = conducteurs.map(conducteur => {
        const data = years.map(annee => {
            // Recherche du record correspondant à l'année et au conducteur courant
            const record = delaysData.find(item => item.annee === annee && item.conducteur === conducteur);
            return record ? record.avgReel : 0;
        });
        // Utilisation d'une couleur fixe si le conducteur est défini, sinon sélection aléatoire dans fallbackColors
        const backgroundColor = fixedColors[conducteur]
            ? fixedColors[conducteur]
            : fallbackColors[Math.floor(Math.random() * fallbackColors.length)];

        return {
            label: conducteur,
            data: data,
            backgroundColor: backgroundColor
        };
    });

    // Création du graphique en barres
    new Chart(document.getElementById('chartDelays'), {
        type: 'bar',
        data: {
            labels: years,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Délais Réception Moyen (en mois) par Année et Conducteur'
                },
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Nombre de mois'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Année'
                    }
                }
            }
        }
    });
</script>
{% endblock %}
