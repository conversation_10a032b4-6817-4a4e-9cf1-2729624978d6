{% extends 'base.html.twig' %}

{% block title %}Connexion{% endblock %}

{% block body %}
    <div class="d-flex align-items-center justify-content-center" style="margin: 0 5%;margin-top: 15vh;">
        <div class="row shadow-lg rounded overflow-hidden" style="width: 60%; background-color: white;">
            <!-- Section Image -->
            <div class="col-7 p-0" style="height: auto;">
                <img src="/bbc.jpg" alt="Illustration"  style="object-fit: cover; height: 100%; width: 100%;">
            </div>

            <!-- Section Formulaire -->
            <div class="col-5 d-flex flex-column px-3 py-5" style="box-shadow: 2px 0 150px rgba(0, 0, 0, 0.5);">

                {% if app.user %}
                    <div class="mb-3">
                        Vous êtes connecté en tant que {{ app.user.username }}, <a href="{{ path('app_logout') }}">Déconnexion</a>
                    </div>
                {% else %}


                <h1 class="h4 mb-4 text-center">Connexion</h1>
                <form method="post">
                    <div class="form-group mb-3">
                        <label for="username">Email</label>
                        <input type="text" value="{{ last_username }}" name="_username" id="username" class="form-control {% if error %}is-invalid{% endif %}" autocomplete="username" required autofocus>
                    </div>
                    <div class="form-group mb-3">
                        <label for="password">Mot de passe</label>
                        <input type="password" name="_password" id="password" class="form-control {% if error %}is-invalid{% endif %}" autocomplete="current-password" required>
                    </div>

                    <input type="hidden" name="_csrf_token"
                           value="{{ csrf_token('authenticate') }}"
                    >

                    <button class="btn btn-primary w-100" type="submit">Se connecter</button>
                </form>
                {% endif %}
            </div>
        </div>
    </div>

    <style>
        body {
            background: url('/bbc_blur.png') no-repeat center center fixed;
            background-size: cover;
        }

    </style>

    {% if error %}
    <script>
        Toast.fire({
            icon: 'error',
            title: 'Identifiants incorrects'
        })
    </script>
    
    {% endif %}

{% endblock %}
