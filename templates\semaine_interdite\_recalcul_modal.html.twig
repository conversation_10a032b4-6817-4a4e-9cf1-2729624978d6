{# Modal pour afficher les résultats du recalcul des chantiers #}
<div class="modal fade" id="recalculModal" tabindex="-1" aria-labelledby="recalculModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recalculModalLabel">
                    <i class="fas fa-calculator me-2"></i>
                    Résultats du recalcul des chantiers
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="recalculResults">
                    <!-- Les résultats seront injectés ici via JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="recalculerManuellement">
                    <i class="fas fa-sync-alt me-2"></i>
                    Recalculer manuellement
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour afficher les résultats du recalcul
    function afficherResultatsRecalcul(resultats) {
        const container = document.getElementById('recalculResults');
        
        if (resultats.chantiersModifies === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Aucun chantier n'a été modifié.</strong><br>
                    ${resultats.chantiersRecalcules} chantier(s) ont été vérifiés.
                </div>
            `;
        } else {
            let html = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Recalcul terminé avec succès !</strong><br>
                    ${resultats.chantiersRecalcules} chantier(s) vérifiés, 
                    ${resultats.chantiersModifies} chantier(s) modifiés, 
                    ${resultats.etapesTotalesModifiees} étape(s) recalculées.
                </div>
            `;
            
            if (resultats.details && resultats.details.length > 0) {
                html += `
                    <h6 class="mt-3 mb-2">Détails des modifications :</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Chantier</th>
                                    <th>Client</th>
                                    <th>Commune</th>
                                    <th>Étapes modifiées</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                resultats.details.forEach(function(detail) {
                    const etapesModifiees = Object.keys(detail.etapesModifiees).map(function(etape) {
                        const modification = detail.etapesModifiees[etape];
                        return `${etape}: S${modification.ancienne} → S${modification.nouvelle}`;
                    }).join('<br>');
                    
                    html += `
                        <tr>
                            <td>#${detail.chantierId}</td>
                            <td>${detail.client}</td>
                            <td>${detail.commune}</td>
                            <td><small>${etapesModifiees}</small></td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }
        }
        
        container.innerHTML = html;
    }
    
    // Gestionnaire pour le recalcul manuel
    document.getElementById('recalculerManuellement').addEventListener('click', function() {
        const btn = this;
        const originalText = btn.innerHTML;
        
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Recalcul en cours...';
        
        fetch('/semaines-interdites/recalculer-chantiers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                afficherResultatsRecalcul(data.resultats);
                
                // Afficher une notification de succès
                if (typeof showNotification === 'function') {
                    showNotification('Recalcul terminé avec succès', 'success');
                }
            } else {
                document.getElementById('recalculResults').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Erreur lors du recalcul :</strong><br>
                        ${data.message || 'Une erreur inconnue s\'est produite'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            document.getElementById('recalculResults').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Erreur de communication avec le serveur</strong>
                </div>
            `;
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    });
    
    // Fonction globale pour afficher le modal avec les résultats
    window.afficherModalRecalcul = function(resultats) {
        afficherResultatsRecalcul(resultats);
        const modal = new bootstrap.Modal(document.getElementById('recalculModal'));
        modal.show();
    };
});
</script>
