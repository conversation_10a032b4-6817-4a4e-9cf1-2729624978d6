{% extends 'base.html.twig' %}

{% block title %}Semaines Interdites{% endblock %}

{% block body %}
<div class="container mt-3">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold text-dark mb-1">
                        <i class="bi bi-calendar-x me-2 text-danger"></i>Semaines Interdites
                    </h2>
                    <p class="text-muted mb-0">G<PERSON>rez les semaines à éviter lors du calcul des étapes</p>
                </div>
                <div class="d-flex gap-3 align-items-center">
                    <!-- Filtre par année -->
                    <select class="form-select" id="filtreAnnee" style="width: auto;">
                        <option value="all">Toutes les années</option>
                        {% for annee in range(anneeCourante, anneeCourante + 3) %}
                            <option value="{{ annee }}" {% if annee == anneeCourante %}selected{% endif %}>{{ annee }}</option>
                        {% endfor %}
                    </select>
                    
                    <!-- Bouton d'ajout -->
                    <button class="btn btn-primary px-4" data-bs-toggle="modal" data-bs-target="#addSemaineModal">
                        <i class="bi bi-plus-circle me-2"></i>Nouvelle Semaine Interdite
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-x display-4 text-danger mb-2"></i>
                    <h5 class="fw-bold" id="totalSemaines">{{ semainesParAnnee|length > 0 ? semainesParAnnee|map(s => s|length)|reduce((a, b) => a + b) : 0 }}</h5>
                    <small class="text-muted">Total semaines interdites</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-event display-4 text-warning mb-2"></i>
                    <h5 class="fw-bold" id="semainesAnneeActuelle">{{ semainesParAnnee[anneeCourante]|default([])|length }}</h5>
                    <small class="text-muted">Cette année ({{ anneeCourante }})</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-plus display-4 text-success mb-2"></i>
                    <h5 class="fw-bold" id="semainesAnneeSuivante">{{ semainesParAnnee[anneeCourante + 1]|default([])|length }}</h5>
                    <small class="text-muted">Année suivante ({{ anneeCourante + 1 }})</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-range display-4 text-info mb-2"></i>
                    <h5 class="fw-bold">{{ semainesParAnnee|keys|length }}</h5>
                    <small class="text-muted">Années concernées</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des semaines interdites -->
    <div class="row">
        {% if semainesParAnnee|length > 0 %}
            {% for annee, semaines in semainesParAnnee %}
            <div class="col-12 mb-4 annee-section" data-annee="{{ annee }}">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle me-3 d-flex align-items-center justify-content-center bg-primary bg-opacity-10" 
                                     style="width: 40px; height: 40px;">
                                    <i class="bi bi-calendar3 text-primary"></i>
                                </div>
                                <div>
                                    <h4 class="mb-0 fw-bold text-dark">Année {{ annee }}</h4>
                                    <small class="text-muted">{{ semaines|length }} semaine{{ semaines|length > 1 ? 's' : '' }} interdite{{ semaines|length > 1 ? 's' : '' }}</small>
                                </div>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addSemaineModal" onclick="$('#annee').val('{{ annee }}')">
                                <i class="bi bi-plus me-1"></i>Ajouter
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body pt-0">
                        <div class="row g-3">
                            {% for semaine in semaines %}
                            <div class="col-lg-4 col-md-6">
                                <div class="semaine-card border rounded-3 p-3 h-100 position-relative" data-semaine-id="{{ semaine.id }}">
                                    <!-- Border left rouge -->
                                    <div class="position-absolute top-0 start-0 h-100 bg-danger rounded-start" style="width: 4px;"></div>
                                    
                                    <div class="ps-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="fw-bold text-dark mb-1">{{ semaine.semaineFormatee }}</h6>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline" type="button" data-bs-toggle="dropdown">
                                                    <i class="bi bi-three-dots"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item edit-semaine" href="#" data-id="{{ semaine.id }}"><i class="bi bi-pencil me-2"></i>Modifier</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger delete-semaine" href="#" data-id="{{ semaine.id }}"><i class="bi bi-trash me-2"></i>Supprimer</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        {% set dates = semaine.datesDebutFin %}
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="bi bi-calendar-range me-1"></i>
                                                {{ dates.debut|date('d/m') }} - {{ dates.fin|date('d/m/Y') }}
                                            </small>
                                        </div>
                                        
                                        {% if semaine.motif %}
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="bi bi-chat-text me-1"></i>
                                                <span class="motif-text">{{ semaine.motif }}</span>
                                            </small>
                                        </div>
                                        {% endif %}
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-person me-1"></i>{{ semaine.creePar }}
                                            </small>
                                            {% if semaine.estPassee %}
                                                <span class="badge bg-secondary">Passée</span>
                                            {% else %}
                                                <span class="badge bg-warning">À venir</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="bi bi-calendar-check display-1 text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune semaine interdite</h5>
                    <p class="text-muted mb-4">Commencez par ajouter des semaines à éviter lors du calcul des étapes</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSemaineModal">
                        <i class="bi bi-plus-circle me-2"></i>Ajouter la première semaine interdite
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal d'ajout -->
<div class="modal fade" id="addSemaineModal" tabindex="-1" aria-labelledby="addSemaineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 pb-0">
                <div>
                    <h5 class="modal-title fw-bold text-dark" id="addSemaineModalLabel">
                        <i class="bi bi-calendar-x me-2 text-danger"></i>Nouvelle Semaine Interdite
                    </h5>
                    <p class="text-muted mb-0 small">Ajouter une semaine à éviter lors des calculs</p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addSemaineForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="numeroSemaine" class="form-label fw-semibold text-dark">
                                <i class="bi bi-calendar-week me-1"></i>Numéro de semaine
                            </label>
                            <input type="number" class="form-control form-control-lg" id="numeroSemaine" name="numeroSemaine" 
                                   min="1" max="53" required placeholder="1-53">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="annee" class="form-label fw-semibold text-dark">
                                <i class="bi bi-calendar3 me-1"></i>Année
                            </label>
                            <select class="form-select form-select-lg" id="annee" name="annee" required>
                                {% for annee in range(anneeCourante, anneeCourante + 5) %}
                                    <option value="{{ annee }}" {% if annee == anneeCourante %}selected{% endif %}>{{ annee }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="motif" class="form-label fw-semibold text-dark">
                            <i class="bi bi-chat-text me-1"></i>Motif (optionnel)
                        </label>
                        <textarea class="form-control" id="motif" name="motif" rows="3" 
                                  placeholder="Raison de l'interdiction (congés, fermeture, etc.)"></textarea>
                    </div>
                    <div id="semainePreview" class="alert alert-info" style="display: none;">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="previewText"></span>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-danger px-4">
                        <i class="bi bi-calendar-x me-1"></i>Ajouter la semaine interdite
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition -->
<div class="modal fade" id="editSemaineModal" tabindex="-1" aria-labelledby="editSemaineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 pb-0">
                <div>
                    <h5 class="modal-title fw-bold text-dark" id="editSemaineModalLabel">
                        <i class="bi bi-pencil me-2 text-primary"></i>Modifier Semaine Interdite
                    </h5>
                    <p class="text-muted mb-0 small">Modifier le motif de la semaine interdite</p>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSemaineForm">
                <input type="hidden" id="editSemaineId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label fw-semibold text-dark">
                            <i class="bi bi-calendar-week me-1"></i>Semaine
                        </label>
                        <div class="form-control-plaintext fw-bold" id="editSemaineInfo"></div>
                    </div>
                    <div class="mb-3">
                        <label for="editMotif" class="form-label fw-semibold text-dark">
                            <i class="bi bi-chat-text me-1"></i>Motif
                        </label>
                        <textarea class="form-control" id="editMotif" name="motif" rows="3" 
                                  placeholder="Raison de l'interdiction (congés, fermeture, etc.)"></textarea>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="bi bi-x me-1"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="bi bi-check me-1"></i>Sauvegarder
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Prévisualisation de la semaine lors de la saisie
    function updatePreview() {
        const numeroSemaine = $('#numeroSemaine').val();
        const annee = $('#annee').val();

        if (numeroSemaine && annee) {
            // Calculer les dates de la semaine
            const date = new Date(annee, 0, 1 + (numeroSemaine - 1) * 7);
            const day = date.getDay();
            const diff = date.getDate() - day + (day == 0 ? -6 : 1);
            const monday = new Date(date.setDate(diff));
            const sunday = new Date(monday);
            sunday.setDate(monday.getDate() + 6);

            const options = { day: '2-digit', month: '2-digit' };
            const mondayStr = monday.toLocaleDateString('fr-FR', options);
            const sundayStr = sunday.toLocaleDateString('fr-FR', { ...options, year: 'numeric' });

            $('#previewText').text(`Semaine ${numeroSemaine} - ${annee} (${mondayStr} - ${sundayStr})`);
            $('#semainePreview').show();
        } else {
            $('#semainePreview').hide();
        }
    }

    $('#numeroSemaine, #annee').on('input change', updatePreview);

    // Filtre par année
    $('#filtreAnnee').on('change', function() {
        const anneeSelectionnee = $(this).val();

        if (anneeSelectionnee === 'all') {
            $('.annee-section').show();
        } else {
            $('.annee-section').hide();
            $(`.annee-section[data-annee="${anneeSelectionnee}"]`).show();
        }
    });

    // Ajout d'une nouvelle semaine interdite
    $('#addSemaineForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: "{{ path('app_semaines_interdites_add') }}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Toast.fire({
                        icon: 'success',
                        title: response.message
                    });

                    $('#addSemaineModal').modal('hide');
                    location.reload(); // Recharger la page pour afficher la nouvelle semaine
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                Toast.fire({
                    icon: 'error',
                    title: response.error || 'Erreur lors de l\'ajout'
                });
            }
        });
    });

    // Édition d'une semaine interdite
    $(document).on('click', '.edit-semaine', function(e) {
        e.preventDefault();

        const semaineId = $(this).data('id');
        const card = $(this).closest('.semaine-card');
        const semaineInfo = card.find('h6').text();
        const motifActuel = card.find('.motif-text').text() || '';

        $('#editSemaineId').val(semaineId);
        $('#editSemaineInfo').text(semaineInfo);
        $('#editMotif').val(motifActuel);

        $('#editSemaineModal').modal('show');
    });

    // Sauvegarde de l'édition
    $('#editSemaineForm').on('submit', function(e) {
        e.preventDefault();

        const semaineId = $('#editSemaineId').val();
        const motif = $('#editMotif').val();

        $.ajax({
            url: "{{ path('app_semaines_interdites_edit', {'id': '0'}) }}".replace('0', semaineId),
            type: 'PUT',
            data: { motif: motif },
            success: function(response) {
                if (response.success) {
                    Toast.fire({
                        icon: 'success',
                        title: response.message
                    });

                    $('#editSemaineModal').modal('hide');
                    location.reload();
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                Toast.fire({
                    icon: 'error',
                    title: response.error || 'Erreur lors de la modification'
                });
            }
        });
    });

    // Suppression d'une semaine interdite
    $(document).on('click', '.delete-semaine', function(e) {
        e.preventDefault();

        const semaineId = $(this).data('id');
        const card = $(this).closest('.semaine-card');
        const semaineInfo = card.find('h6').text();

        Swal.fire({
            title: 'Supprimer cette semaine interdite ?',
            html: `Êtes-vous sûr de vouloir supprimer <strong>${semaineInfo}</strong> ?<br><small class="text-muted">Cette action est irréversible.</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-trash me-1"></i>Oui, supprimer',
            cancelButtonText: '<i class="bi bi-x me-1"></i>Annuler',
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "{{ path('app_semaines_interdites_delete', {'id': '0'}) }}".replace('0', semaineId),
                    type: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            Toast.fire({
                                icon: 'success',
                                title: response.message
                            });

                            card.closest('.col-lg-4').fadeOut(300, function() {
                                $(this).remove();

                                // Vérifier si la section est vide
                                const section = card.closest('.annee-section');
                                if (section.find('.semaine-card').length === 0) {
                                    location.reload();
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        const response = JSON.parse(xhr.responseText);
                        Toast.fire({
                            icon: 'error',
                            title: response.error || 'Erreur lors de la suppression'
                        });
                    }
                });
            }
        });
    });

    // Réinitialiser le modal à la fermeture
    $('#addSemaineModal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        $('#semainePreview').hide();
    });

    // Initialiser la prévisualisation
    updatePreview();
});
</script>

<style>
/* Styles pour la page des semaines interdites */
.semaine-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef !important;
    transition: all 0.3s ease;
    cursor: default;
}

.semaine-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    border-color: #dee2e6 !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.semaine-card {
    animation: fadeInUp 0.5s ease-out;
}

/* Styles pour les dropdowns */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Styles pour le modal */
.modal-content {
    border-radius: 12px;
}

.modal-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px 12px 0 0;
}

/* Responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 15px;
    }

    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }
}

@media (max-width: 576px) {
    .semaine-card {
        margin-bottom: 1rem;
    }

    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 1rem;
    }
}
</style>

{% endblock %}
