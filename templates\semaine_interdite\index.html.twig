{% extends 'base.html.twig' %}

{% block title %}Se<PERSON><PERSON> Interdites{% endblock %}

{% block body %}
<div class="container mt-3">
    <!-- Header avec explication -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1 me-4">
                    <div class="d-flex justify-content-between align-items-center">
                        
                        <h2 class="fw-bold text-dark mb-2">
                            <i class="bi bi-calendar-x me-2 text-danger"></i>Semaines Interdites
                        </h2>

                        
                        <div class="d-flex flex-column gap-2">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSemaineModal">
                                <i class="bi bi-plus-circle me-2"></i>Ajouter une semaine
                            </button>
                        </div>
                    </div>

                    <!-- Explication claire -->
                    <div class="alert alert-info border-0 mb-3" style="background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-info-circle-fill text-info me-3 mt-1"></i>
                            <div>
                                <h6 class="fw-bold mb-2 text-dark">Comment ça fonctionne ?</h6>
                                <p class="mb-2 small">
                                    Les semaines interdites sont automatiquement évitées lors du calcul des étapes de chantier.
                                    <strong>Quand vous ajoutez ou supprimez une semaine interdite</strong> :
                                </p>
                                <ul class="mb-0 small">
                                    <li>✅ <strong>Tous les chantiers en cours sont recalculés automatiquement</strong></li>
                                    <li>✅ Les étapes qui tombent sur ces semaines sont décalées</li>
                                    <li>⚠️ <strong>Les étapes marquées "FAIT" ne sont jamais modifiées</strong></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Statistiques simplifiées -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-calendar-x h3 text-danger mb-2"></i>
                    <h5 class="fw-bold mb-1">{{ semainesParAnnee|length > 0 ? semainesParAnnee|map(s => s|length)|reduce((a, b) => a + b) : 0 }}</h5>
                    <small class="text-muted">Semaines interdites</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-calendar-event h3 text-warning mb-2"></i>
                    <h5 class="fw-bold mb-1">{{ semainesParAnnee[anneeCourante]|default([])|length }}</h5>
                    <small class="text-muted">Cette année ({{ anneeCourante }})</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-calendar-plus h3 text-success mb-2"></i>
                    <h5 class="fw-bold mb-1">{{ semainesParAnnee[anneeCourante + 1]|default([])|length }}</h5>
                    <small class="text-muted">Année suivante ({{ anneeCourante + 1 }})</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste simplifiée des semaines interdites -->
    <div class="row">
        {% if semainesParAnnee|length > 0 %}
            {% for annee, semaines in semainesParAnnee %}
            <div class="col-12 mb-3 annee-section" data-annee="{{ annee }}">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light border-0 py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold text-dark">
                                <i class="bi bi-calendar3 me-2 text-primary"></i>{{ annee }}
                                <small class="text-muted ms-2">({{ semaines|length }} semaine{{ semaines|length > 1 ? 's' : '' }})</small>
                            </h5>
                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addSemaineModal" onclick="$('#annee').val('{{ annee }}')">
                                <i class="bi bi-plus me-1"></i>Ajouter
                            </button>
                        </div>
                    </div>

                    <div class="card-body py-3">
                        <div class="row g-2">
                            {% for semaine in semaines %}
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="semaine-card border rounded p-2 position-relative" data-semaine-id="{{ semaine.id }}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="fw-bold text-dark mb-1">{{ semaine.semaineFormatee }}</h6>
                                            {% set dates = semaine.datesDebutFin %}
                                            <small class="text-muted d-block">{{ dates.debut|date('d/m') }} - {{ dates.fin|date('d/m') }}</small>
                                            {% if semaine.motif %}
                                                <small class="text-muted d-block mt-1">
                                                    <i class="bi bi-chat-text me-1"></i><span class="motif-text">{{ semaine.motif }}</span>
                                                </small>
                                            {% endif %}
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-link text-muted p-1" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li><a class="dropdown-item edit-semaine" href="#" data-id="{{ semaine.id }}">
                                                    <i class="bi bi-pencil me-2"></i>Modifier
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger delete-semaine" href="#" data-id="{{ semaine.id }}">
                                                    <i class="bi bi-trash me-2"></i>Supprimer
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <small class="text-muted">
                                            <i class="bi bi-person me-1"></i>{{ semaine.creePar }}
                                        </small>
                                        {% if semaine.estPassee %}
                                            <span class="badge bg-secondary badge-sm">Passée</span>
                                        {% else %}
                                            <span class="badge bg-warning badge-sm">À venir</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-calendar-check h1 text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">Aucune semaine interdite configurée</h5>
                        <p class="text-muted mb-4">
                            Les semaines interdites permettent d'éviter automatiquement certaines périodes lors du calcul des étapes de chantier.
                        </p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSemaineModal">
                            <i class="bi bi-plus-circle me-2"></i>Ajouter la première semaine
                        </button>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Modal de résultats de recalcul -->
<div class="modal fade" id="recalculModal" tabindex="-1" aria-labelledby="recalculModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recalculModalLabel">
                    <i class="bi bi-arrow-clockwise me-2"></i>Résultats du recalcul
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="recalculResults"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajout simplifié -->
<div class="modal fade" id="addSemaineModal" tabindex="-1" aria-labelledby="addSemaineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSemaineModalLabel">
                    <i class="bi bi-calendar-x me-2 text-danger"></i>Ajouter une semaine interdite
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addSemaineForm">
                <div class="modal-body">
                    <div class="alert alert-info border-0 mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>Cette semaine sera automatiquement évitée lors du calcul des étapes. Les chantiers en cours seront recalculés.</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="numeroSemaine" class="form-label">Numéro de semaine</label>
                            <input type="number" class="form-control" id="numeroSemaine" name="numeroSemaine"
                                   min="1" max="53" required placeholder="1-53">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="annee" class="form-label">Année</label>
                            <select class="form-select" id="annee" name="annee" required>
                                {% for annee in range(anneeCourante, anneeCourante + 5) %}
                                    <option value="{{ annee }}" {% if annee == anneeCourante %}selected{% endif %}>{{ annee }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="motif" class="form-label">Motif (optionnel)</label>
                        <input type="text" class="form-control" id="motif" name="motif"
                               placeholder="Ex: Congés, fermeture, maintenance...">
                    </div>

                    <div id="semainePreview" class="alert alert-light border" style="display: none;">
                        <i class="bi bi-calendar-range me-2"></i>
                        <span id="previewText"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-calendar-x me-1"></i>Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition simplifié -->
<div class="modal fade" id="editSemaineModal" tabindex="-1" aria-labelledby="editSemaineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSemaineModalLabel">
                    <i class="bi bi-pencil me-2 text-primary"></i>Modifier le motif
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSemaineForm">
                <input type="hidden" id="editSemaineId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Semaine</label>
                        <div class="form-control-plaintext fw-bold" id="editSemaineInfo"></div>
                    </div>
                    <div class="mb-3">
                        <label for="editMotif" class="form-label">Motif</label>
                        <input type="text" class="form-control" id="editMotif" name="motif"
                               placeholder="Ex: Congés, fermeture, maintenance...">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check me-1"></i>Sauvegarder
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Prévisualisation de la semaine lors de la saisie
    function updatePreview() {
        const numeroSemaine = $('#numeroSemaine').val();
        const annee = $('#annee').val();

        if (numeroSemaine && annee) {
            // Calculer les dates de la semaine
            const date = new Date(annee, 0, 1 + (numeroSemaine - 1) * 7);
            const day = date.getDay();
            const diff = date.getDate() - day + (day == 0 ? -6 : 1);
            const monday = new Date(date.setDate(diff));
            const sunday = new Date(monday);
            sunday.setDate(monday.getDate() + 6);

            const options = { day: '2-digit', month: '2-digit' };
            const mondayStr = monday.toLocaleDateString('fr-FR', options);
            const sundayStr = sunday.toLocaleDateString('fr-FR', { ...options, year: 'numeric' });

            $('#previewText').text(`Semaine ${numeroSemaine} - ${annee} (${mondayStr} - ${sundayStr})`);
            $('#semainePreview').show();
        } else {
            $('#semainePreview').hide();
        }
    }

    $('#numeroSemaine, #annee').on('input change', updatePreview);


    // Ajout d'une nouvelle semaine interdite
    $('#addSemaineForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: "{{ path('app_semaines_interdites_add') }}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $('#addSemaineModal').modal('hide');
                    location.reload();
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                Toast.fire({
                    icon: 'error',
                    title: response.error || 'Erreur lors de l\'ajout'
                });
            }
        });
    });

    // Édition d'une semaine interdite
    $(document).on('click', '.edit-semaine', function(e) {
        e.preventDefault();

        let semaineId = $(this).data('id');
        let card = $(this).closest('.semaine-card');
        let semaineInfo = card.find('h6').text();
        let motifActuel = card.find('.motif-text').text() || '';

        $('#editSemaineId').val(semaineId);
        $('#editSemaineInfo').text(semaineInfo);
        $('#editMotif').val(motifActuel);

        $('#editSemaineModal').modal('show');
    });

    // Sauvegarde de l'édition
    $('#editSemaineForm').on('submit', function(e) {
        e.preventDefault();

        const semaineId = $('#editSemaineId').val();
        const motif = $('#editMotif').val();

        $.ajax({
            url: "{{ path('app_semaines_interdites_edit', {'id': '0'}) }}".replace('0', semaineId),
            type: 'PUT',
            data: { motif: motif },
            success: function(response) {
                if (response.success) {
                    $('#editSemaineModal').modal('hide');
                    location.reload();
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                Toast.fire({
                    icon: 'error',
                    title: response.error || 'Erreur lors de la modification'
                });
            }
        });
    });

    // Suppression d'une semaine interdite
    $(document).on('click', '.delete-semaine', function(e) {
        e.preventDefault();

        const semaineId = $(this).data('id');
        const card = $(this).closest('.semaine-card');
        const semaineInfo = card.find('h6').text();

        Swal.fire({
            title: 'Supprimer cette semaine interdite ?',
            html: `Êtes-vous sûr de vouloir supprimer <strong>${semaineInfo}</strong> ?<br><small class="text-muted">Cette action est irréversible.</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<i class="bi bi-trash me-1"></i>Oui, supprimer',
            cancelButtonText: '<i class="bi bi-x me-1"></i>Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-danger me-2',
                cancelButton: 'btn btn-sm btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "{{ path('app_semaines_interdites_delete', {'id': '0'}) }}".replace('0', semaineId),
                    type: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    },
                    error: function(xhr) {
                        const response = JSON.parse(xhr.responseText);
                        Toast.fire({
                            icon: 'error',
                            title: response.error || 'Erreur lors de la suppression'
                        });
                    }
                });
            }
        });
    });

    // Réinitialiser le modal à la fermeture
    $('#addSemaineModal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
        $('#semainePreview').hide();
    });

    // Initialiser la prévisualisation
    updatePreview();
});
</script>

<style>
/* Styles simplifiés pour la page des semaines interdites */
.semaine-card {
    background: #ffffff;
    border: 1px solid #e9ecef !important;
    transition: all 0.2s ease;
    cursor: default;
}

.semaine-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
    border-color: #dee2e6 !important;
}

/* Animation de rotation pour le bouton recalcul */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Badges plus petits */
.badge-sm {
    font-size: 0.7em;
    padding: 0.25em 0.5em;
}

/* Styles pour les dropdowns */
.dropdown-menu {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 6px;
}

.dropdown-item {
    padding: 0.4rem 0.8rem;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Alerte d'explication */
.alert {
    border-radius: 8px;
}

/* Responsive */
@media (max-width: 768px) {
    .d-flex.flex-column.gap-2 {
        flex-direction: row !important;
        gap: 0.5rem !important;
    }

    .d-flex.flex-column.gap-2 .btn {
        font-size: 0.9em;
        padding: 0.4rem 0.8rem;
    }
}

@media (max-width: 576px) {
    .col-lg-3.col-md-4.col-sm-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .semaine-card {
        margin-bottom: 0.5rem;
    }
}
</style>

{% endblock %}
