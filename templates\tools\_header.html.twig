<nav class="navbar navbar-expand bg-white p-0" style="border-bottom: 2px solid #4DA725">
    <a class="navbar-brand p-0" href="{{ path('app_chantier') }}" style="border: none">
        <img src="/logo_petit_bbc.png" alt="Logo" width="50" height="50" class="d-inline-block align-text-top"> 
    </a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarsExample02" aria-controls="navbarsExample02" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarsExample02">
        <ul class="navbar-nav mr-auto">
        {% if app.user %}
            <li class="nav-item ms-1">
                <a class="nav-link" href="{{ path('app_chantier') }}">Chantiers</a>
            </li>
            {% if not is_granted('ROLE_WATCHER') %}
            <li class="nav-item ms-1">
                <a class="nav-link" href="{{ path('list_artisans') }}">Artisans</a>
            </li>
            {% endif %}
            <li class="nav-item ms-1">
                <a class="nav-link" href="{{ path('app_chantier_statistique') }}">Statistiques</a>
            </li>
        {% endif %}
        </ul>
    </div>
    {% if app.user %}
    <p id="logout" class="m-0 pe-2 text-muted">{{ app.user.username }} <i class="bi bi-person-circle"></i></p>
    {% endif %}
</nav>

<script>
// if logout click ask swal2 confirmation for redirect to logout
$('#logout').on('click', function() {
    Swal.fire({
        title: 'Déconnexion',
        text: "Voulez-vous vraiment vous déconnecter ?",
        showCancelButton: true,
        confirmButtonText: 'Déconnexion',
        cancelButtonText: 'Annuler',
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = "{{ path('app_logout') }}";
        }
    });
});
</script>

<style>
a.nav-link {
    color:rgb(91, 91, 91);
}

#logout {
    cursor: pointer;
}
</style>
