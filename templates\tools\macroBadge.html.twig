{# Dans le fichier de votre macro #}
{% macro etatBadge(chantier, fields) %}
    {% if chantier is not null and attribute(chantier, fields[0]) is not null and attribute(chantier, fields[1]) is not null %}
        {% set week = attribute(chantier, fields[0]) %}
        {% set etat = attribute(chantier, fields[1]) %}

        {% if etat == 'FAIT' %}
            <span  class="badge rounded-pill text-bg-success etat-badge" attr="{{fields[1]}}" etat="{{ etat }}" ><i class="bi bi-check"></i></span>
            <span  class="badge rounded-pill text-bg-success edit-badge" attr="{{fields[0]}}" week="{{ week }}" style="width: 2.7rem">S{{week}}</span>
        {% elseif etat == 'PAS FAIT' %}
            <span  class="badge rounded-pill text-bg-warning etat-badge" attr="{{fields[1]}}" etat="{{ etat }}" ><i class="bi bi-hourglass-split"></i></span>
            <span  class="badge rounded-pill text-bg-warning edit-badge" attr="{{fields[0]}}" week="{{ week }}" style="width: 2.7rem">S{{week}}</span>
        {% elseif etat == 'NON' %}
            <span  class="badge rounded-pill text-bg-dark etat-badge" attr="{{fields[1]}}" etat="{{ etat }}"><i class="bi bi-x"></i></span>
            <span  class="badge rounded-pill text-bg-dark edit-badge" attr="{{fields[0]}}" week="{{ week }}" style="width: 2.7rem">Non</span>
        {% endif %}
    {% elseif chantier is not null and attribute(chantier, fields[1]) is not null %}
        {% set etat = attribute(chantier, fields[1]) %}
        {%  if etat == 'NON' %}
            <span  class="badge rounded-pill text-bg-dark etat-badge" attr="{{fields[1]}}" etat="{{ etat }}"><i class="bi bi-x"></i></span>
            <span  class="badge rounded-pill text-bg-dark edit-badge" attr="{{fields[0]}}" week="" style="width: 2.7rem">Non</span>
        {% elseif etat == 'FAIT' %}
            <span  class="badge rounded-pill text-bg-success etat-badge" attr="{{fields[1]}}" etat="{{ etat }}"><i class="bi bi-check"></i></span>
            <span  class="badge rounded-pill text-bg-success edit-badge" attr="{{fields[0]}}" week="" style="width: 2.7rem"><i class="bi bi-pencil-fill"></i></span>
        {% elseif etat == 'PAS FAIT' %}
            <span  class="badge rounded-pill text-bg-warning etat-badge" attr="{{fields[1]}}" etat="{{ etat }}"><i class="bi bi-hourglass-split"></i></span>
            <span  class="badge rounded-pill text-bg-warning edit-badge" attr="{{fields[0]}}" week="" style="width: 2.7rem"><i class="bi bi-pencil-fill"></i></span>
        {% elseif etat != '' %}
            <span  class="badge rounded-pill text-bg-dark">{{etat}}</span>
        {% else %}
            <span  class="badge rounded-pill text-bg-secondary etat-badge" attr="{{fields[1]}}" etat="{{ etat }}"><i class="bi bi-pencil"></i></span>
            <span  class="badge rounded-pill text-bg-secondary edit-badge" attr="{{fields[0]}}" week="" style="width: 2.7rem"><i class="bi bi-pencil-fill"></i></span>
        {% endif %}
    {% else %}
        <span class="badge rounded-pill text-bg-secondary etat-badge" attr="{{fields[1]}}" etat="NON"><i class="bi bi-pencil"></i></span>
        <span class="badge rounded-pill text-bg-secondary edit-badge" attr="{{fields[0]}}" week="" style="width: 2.7rem"><i class="bi bi-pencil-fill"></i></span>
    {% endif %}
{% endmacro %}
