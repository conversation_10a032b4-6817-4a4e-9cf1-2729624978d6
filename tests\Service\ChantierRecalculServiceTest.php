<?php

namespace App\Tests\Service;

use App\Entity\Chantier;
use App\Repository\ChantierRepository;
use App\Service\ChantierRecalculService;
use App\Service\SemaineCalculatorService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

class ChantierRecalculServiceTest extends TestCase
{
    private ChantierRecalculService $service;
    private ChantierRepository $chantierRepository;
    private SemaineCalculatorService $semaineCalculatorService;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->chantierRepository = $this->createMock(ChantierRepository::class);
        $this->semaineCalculatorService = $this->createMock(SemaineCalculatorService::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->service = new ChantierRecalculService(
            $this->chantierRepository,
            $this->semaineCalculatorService,
            $this->entityManager,
            $this->logger
        );
    }

    public function testRecalculerTousLesChantiersEnCours(): void
    {
        // Créer un chantier de test
        $chantier = new Chantier();
        $chantier->setClient('Test Client');
        $chantier->setCommune('Test Commune');
        $chantier->setDateDeb(new \DateTime('2024-01-01'));
        $chantier->setFondation(1);
        $chantier->setDalle(4);

        // Mock du repository pour retourner le chantier
        $this->chantierRepository
            ->expects($this->once())
            ->method('findChantiersEnCours')
            ->willReturn([$chantier]);

        // Mock du service de calcul
        $this->semaineCalculatorService
            ->expects($this->once())
            ->method('rechargerSemainesInterdites');

        $this->semaineCalculatorService
            ->expects($this->once())
            ->method('recalculerChantier')
            ->with($chantier, ChantierRecalculService::ETAPES_DECALAGES)
            ->willReturn([
                'etapesRecalculees' => [
                    'Fondation' => ['semaine' => 1, 'annee' => 2024],
                    'Dalle' => ['semaine' => 5, 'annee' => 2024]
                ],
                'etapesModifiees' => [
                    'Dalle' => [
                        'ancienne' => 4,
                        'nouvelle' => 5,
                        'annee' => 2024,
                        'semainesEvitees' => ['2024-04']
                    ]
                ]
            ]);

        // Mock de l'entity manager
        $this->entityManager
            ->expects($this->once())
            ->method('flush');

        // Exécuter le test
        $resultats = $this->service->recalculerTousLesChantiersEnCours();

        // Vérifications
        $this->assertEquals(1, $resultats['chantiersRecalcules']);
        $this->assertEquals(1, $resultats['chantiersModifies']);
        $this->assertEquals(1, $resultats['etapesTotalesModifiees']);
        $this->assertCount(1, $resultats['details']);
    }

    public function testRecalculerChantierSansModification(): void
    {
        // Créer un chantier de test
        $chantier = new Chantier();
        $chantier->setClient('Test Client');
        $chantier->setCommune('Test Commune');
        $chantier->setDateDeb(new \DateTime('2024-01-01'));

        // Mock du repository pour retourner le chantier
        $this->chantierRepository
            ->expects($this->once())
            ->method('findChantiersEnCours')
            ->willReturn([$chantier]);

        // Mock du service de calcul
        $this->semaineCalculatorService
            ->expects($this->once())
            ->method('rechargerSemainesInterdites');

        $this->semaineCalculatorService
            ->expects($this->once())
            ->method('recalculerChantier')
            ->willReturn([
                'etapesRecalculees' => [],
                'etapesModifiees' => []
            ]);

        // Exécuter le test
        $resultats = $this->service->recalculerTousLesChantiersEnCours();

        // Vérifications
        $this->assertEquals(1, $resultats['chantiersRecalcules']);
        $this->assertEquals(0, $resultats['chantiersModifies']);
        $this->assertEquals(0, $resultats['etapesTotalesModifiees']);
        $this->assertEmpty($resultats['details']);
    }
}
